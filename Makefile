.PHONY: dev-up dev-down

dev-up:
	@echo "Starting local development environment..."
	docker-compose -f docker-compose.yml --env-file .env up -d --build --remove-orphans
	npx supabase start

dev-restart:
	@echo "Restarting local development environment..."
	docker-compose -f docker-compose.yml down
	docker-compose -f docker-compose.yml --env-file .env up -d --build --remove-orphans

dev-down:
	@echo "Stopping local development environment..."
	docker-compose -f docker-compose.yml down
	npx supabase stop

dev-reset-db:
	@echo "Resetting local Supabase database..."
	npx supabase db reset

dev-restart-db:
	@echo "Restarting local development database..."
	npx supabase stop
	npx supabase start

insert-test-data:
	@echo "Please provide the following details for the test company:"
	@read -p "Enter User UUID: " USER_UUID_INPUT; \
	read -p "Enter Company Name: " COMPANY_NAME_INPUT; \
	echo "Preparing seed data for User $$USER_UUID_INPUT and Company '$$COMPANY_NAME_INPUT'..."; \
	\
	echo "Copying seed template to seed.sql..."; \
	cp supabase/seed.template.sql supabase/seed.sql; \
	\
	echo "Replacing variables in seed file..."; \
	sed -i "s/USER_UUID_VAR/$$USER_UUID_INPUT/g" supabase/seed.sql; \
	sed -i "s/COMPANY_NAME_VAR/$$COMPANY_NAME_INPUT/g" supabase/seed.sql; \
	\
	echo "Executing seed file..."; \
	if psql postgres://postgres:postgres@localhost:54322/postgres -f supabase/seed.sql -q > /dev/null; then \
		echo "Test data insertion complete for '$$COMPANY_NAME_INPUT'!"; \
	else \
		echo "ERROR: Seed data insertion failed. Trying with error output..."; \
		psql postgres://postgres:postgres@localhost:54322/postgres -f supabase/seed.sql; \
	fi
