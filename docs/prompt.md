in my sveltekit frontend app @/services/frontend I have a page @/services/frontend/src/routes/demo . This is a demo implementation of paraglide which I use for i18n support. This page make the default language Dutch on the page. I want my whole site to default to Dutch.

Am I correct to assume this must be done in a Layouts file to be globally used?

Analyze the situation and implement this so all my pages work with i18n support.

Not all pages need the switching between languages yet. Only /auth page needs it. Use shadcn-ui components for this. We will need to add some components to do this in a modern way. Make it something with a dropdown so we can easily extend it later with other languages. And make it a special component in the library so it can be re-used. Also use flag icons for the languages. For this you need to use the package @iconify/svelte . Have a look at the following guide how it works with svelte: @https://iconify.design/docs/icon-components/svelte/

use sequential thinking and use context7

To use shadcn-ui you sometime need to import new components so we can use them and change them when needed. The component will be imported in @/services/frontend/src/lib/components . At this moment we have a card and button in there, which we use on the @/services/frontend/src/routes/auth/+page.svelte .

The way I added the button is with the command "npx shadcn-svelte@latest add button". You can list the components and snippets and commands at context7 zippoxer/shadcn-svelte-next

During installation I followed this guide which You can also use to import components:

@https://www.shadcn-svelte.com/docs/installation/sveltekit

I also want you to use Atomic Design for our component library. Here is a high-level guide about the principle. @https://raw.githubusercontent.com/okasi/atomic-design/refs/heads/main/putMeInTheComponentsFolder.md

shadcn-ui library lents itself perfectly for it because we import the component code and we own it and can change it however we like.
