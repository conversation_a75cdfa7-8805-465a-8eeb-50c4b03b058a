# Supabase Production Database Connection Configuration

This document outlines the steps to configure a secure and performant connection to the Supabase production database.

## 1. Environment Variables

Supabase credentials should be stored as environment variables to ensure security and flexibility across different environments.

- `SUPABASE_URL`: The API URL of your Supabase project.
- `SUPABASE_ANON_KEY`: The `anon` public key for your Supabase project.

**Example (.env file):**

```
SUPABASE_URL="https://your-project-ref.supabase.co"
SUPABASE_ANON_KEY="your-anon-public-key"
```

## 2. Connection Pooling

For optimal performance and efficient resource utilization, especially in serverless environments, it is recommended to use connection pooling. Supabase provides a built-in PgBouncer for connection pooling.

To configure connection pooling in your application, use the connection string provided by Supabase that points to the PgBouncer port (usually `6543`).

**Example (Node.js with `pg`):**

```javascript
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL_WITH_PGBOUNCER, // e.g., **********************************/database
  ssl: {
    rejectUnauthorized: true, // Ensure SSL is enabled and verified
  },
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
});

pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

module.exports = {
  query: (text, params) => pool.query(text, params),
};
```

## 3. SSL Configuration

Always ensure SSL is enabled for secure communication between your application and the Supabase database. Supabase connections are typically SSL-enabled by default. When using client libraries, ensure that SSL verification is not disabled.

For Node.js `pg` library, `ssl: { rejectUnauthorized: true }` ensures that the server's SSL certificate is verified against a list of trusted CAs.

## 4. Read/Write Splitting (Advanced)

For applications with high read loads, consider implementing read/write splitting. This involves directing read queries to read replicas and write queries to the primary database. This can significantly improve performance and scalability.

**Note:** This is an advanced configuration and might require specific setup within your application's ORM or database client.

## 5. Documentation

Maintain clear and up-to-date documentation for your production database connection setup, including:
- Environment variable definitions.
- Connection string formats.
- Connection pooling configurations.
- SSL requirements.
- Any read/write splitting logic.