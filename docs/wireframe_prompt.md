SalonIntel Onboarding Workflow Wireframe Prompt
Project Context
Create low-fidelity wireframes for SalonIntel, an AI-powered marketing automation platform for beauty salon owners. Design a multi-step onboarding workflow that collects essential business data to enable personalized AI marketing assistance.

Target Users & Constraints
Primary Users: Working women aged 45-60, beauty salon owners with varying technical comfort levels
Business Context: Small high-end beauty salons, solo operators, new entrepreneurs in beauty industry
Pain Points: Overwhelmed by marketing demands, limited time, need clear, simple interfaces
User Goals: Quick setup, clear progress, minimal friction
Design System Requirements
Inspiration: Basecamp and <PERSON><PERSON>'s minimalist aesthetic
Typography: Clean, readable fonts with clear hierarchy - minimum 16px for body text
Layout: Generous whitespace, single-column layouts, clear visual separation
Color Palette: High contrast ratios (4.5:1 minimum for normal text, 3:1 for large text)
Visual Style: Professional, modern, high-end but approachable
Technical Requirements
Mobile-First: Optimize for mobile devices primarily
Responsive: Tablet and desktop adaptations
Accessibility: WCAG 2.1 Level AA compliance for European standards
Internationalization: Support Dutch (primary) and English languages
Progressive Enhancement: Works without JavaScript
WCAG Level AA Essentials (MVP - Target Audience: Working Women 45-60)
Color Contrast: 4.5:1 for normal text, 3:1 for large text (18px+)
Text Size: Minimum 16px body text, scalable up to 200% without horizontal scrolling
Visual Hierarchy: Clear heading structure (h1, h2, h3) with distinct visual differences
Form Labels: Clearly associated with form controls, visible labels (no placeholder-only)
Error Identification: Clear visual and text indication of errors
Focus Indicators: Visible focus states for interactive elements
Onboarding Flow Structure
Screen 1: Welcome & Overview
Layout: Centered content with hero section

Header: SalonIntel logo + language switcher (NL/EN toggle)
Hero headline: "Let's set up your AI marketing assistant" (h1, large text)
Subtext: Brief explanation of what will be collected and why (readable size)
Progress indicator: Step 1 of 6 (visual progress bar)
Primary CTA: "Start Setup" button (high contrast, minimum 44px touch target)
Secondary action: "Skip for now" link
Footer: Estimated time "Takes about 5 minutes"
Screen 2: Basic Business Information
Layout: Single-column form with logical grouping

Progress: Step 2 of 6 (visual progress bar)
Section header: "Tell us about your beauty salon" (h2)
Form fields with clear labels above inputs:
Beauty salon name* (required indicator visible)
Street address*
City/postal code*
Phone number*
Business email*
Website URL (optional - clearly marked)
Error States: Red border + icon + clear error text below field
Validation: Email format check, required field validation
Navigation: "Back" button + "Continue" primary button (disabled until required fields valid)
Privacy note: Small but readable text about data handling
Screen 3: Services & Business Details
Layout: Form with clear sections

Progress: Step 3 of 6
Section header: "What beauty services do you offer?" (h2)
Service categories (large checkboxes with clear labels):
Facial treatments & skincare
Eyebrow & lash services
Waxing & hair removal
Massage therapy
Nail services (manicure/pedicure)
Hair styling & cuts
Makeup services
Other (text input)
Pricing tier (radio buttons with clear descriptions):
Budget-friendly
Mid-range
Premium/luxury
Business mission (optional textarea): "Describe your beauty salon's unique approach"
Error States: Clear indication if no services selected
Navigation: Back + Continue buttons
Screen 4: Social Media Accounts
Layout: Card-based linking interface

Progress: Step 4 of 6
Section header: "Connect your social accounts" (h2)
Subtext: "Help us understand your current online presence"
Platform cards (large, clear targets):
Instagram (most important for beauty industry)
Facebook
TikTok
LinkedIn (optional)
Each card: Platform logo, "Connect" button, "Skip" option
Alternative: Manual URL input fields with validation
Error States: Invalid URL format indication
Success States: Green checkmark when successfully connected
Navigation: Back + Continue (works even if none connected)
Screen 5: Marketing Goals & Objectives
Layout: Goal selection with visual hierarchy

Progress: Step 5 of 6
Section header: "What are your marketing goals?" (h2)
Subtext: "Select all that apply"
Goal options (large checkboxes with descriptive text):
Attract new clients
Showcase beauty treatments & results
Increase social media engagement
Build customer loyalty & retention
Promote seasonal beauty packages
Competitor analysis
Priority goal: "What's most important right now?" (radio buttons)
Error States: Prompt to select at least one goal
Navigation: Back + Continue buttons
Screen 6: Summary & Confirmation
Layout: Review sections with clear hierarchy

Progress: Step 6 of 6
Header: "Almost done! Review your information" (h1)
Summary sections with edit links:
Beauty salon info (edit link clearly visible)
Services & pricing (edit link)
Connected accounts (edit link)
Marketing goals (edit link)
Next steps preview: Clear explanation of what happens next
Terms acceptance: Large checkbox with readable terms link
Error States: Terms must be accepted to proceed
Primary CTA: "Complete Setup" button (prominent, high contrast)
Navigation: Back button + Complete button
MVP Form Validation & Error Handling
Required Field Validation
Visual indicators: Red border, error icon, error text
Error message: "This field is required" (clear, simple language)
Real-time validation: On field blur/change
Prevent form submission until resolved
Email Validation
Format check: Standard email pattern validation
Error message: "Please enter a valid email address"
Visual feedback: Red border + error text below field
URL Validation (Social Media)
Basic URL format check for manual entries
Error message: "Please enter a valid website URL"
Accept various formats (with/without http://)
Success States
Green border or checkmark icon for validated fields
Success message for completed sections
Visual confirmation when social accounts connected
Error Recovery
Clear, jargon-free error messages
Specific guidance on how to fix errors
Error summary at top of form if multiple errors
Network errors: "Connection problem - please try again"
Loading States
Spinner or progress indicator for API calls
Disabled buttons during processing
Clear feedback when actions are processing
Responsive Behavior
Mobile (320-768px): Single column, large touch targets (minimum 44px), readable text size
Tablet (768-1024px): Maintain single column, larger form fields, side-by-side buttons
Desktop (1024px+): Centered content max-width 600px, preserve mobile patterns
Content Hierarchy
Typography Scale (WCAG AA Compliant)
H1: Page titles (28px mobile, 36px desktop) - high contrast
H2: Section headers (22px mobile, 28px desktop) - high contrast
Body: Form labels and text (16px minimum) - 4.5:1 contrast ratio
Small: Helper text (14px minimum) - 4.5:1 contrast ratio
Error text: 16px, red with sufficient contrast ratio
Visual Indicators
Required fields: Red asterisk (*) + "required" in label
Optional fields: "(optional)" in label
Error states: Red border + error icon + error text
Success states: Green border or checkmark
Focus states: Clear, high-contrast outline
Deliverable Specifications
Create wireframes showing:

User Flow Diagram: Complete journey with decision points and error paths
Individual Screen Wireframes: All 6 screens with proper hierarchy
Error State Examples: Key validation and error scenarios
Success State Examples: Completed form sections and confirmations
Mobile-First Layouts: Primary wireframes for mobile viewport
Responsive Variations: Key differences for tablet/desktop
Accessibility Annotations: WCAG Level AA compliance notes
Form Validation Flows: Error handling and recovery patterns
Success Criteria for MVP
Beauty salon owners aged 45-60 can complete onboarding without confusion
All text meets WCAG AA contrast requirements
Form errors are immediately clear and actionable
Progress is always visible and understandable
Works seamlessly on mobile devices (primary usage)
Collects sufficient data for AI personalization specific to beauty industry
Professional appearance builds trust in beauty industry context
Use simple boxes, lines, and annotations to represent layout structure, content hierarchy, form states, and validation patterns. Focus on information architecture, error handling, and accessibility considerations rather than visual design details.
