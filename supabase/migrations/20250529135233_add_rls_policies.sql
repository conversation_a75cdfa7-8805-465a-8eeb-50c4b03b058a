-- Enable RLS on company_profiles
ALTER TABLE public.company_profiles ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Users can view their own company profile
CREATE POLICY "Users can view their own company profile."
ON public.company_profiles FOR SELECT
TO authenticated
USING ( (SELECT auth.uid()) = user_id );

-- Policy for INSERT: Users can create their own company profile
CREATE POLICY "Users can create their own company profile."
ON public.company_profiles FOR INSERT
TO authenticated
WITH CHECK ( (SELECT auth.uid()) = user_id );

-- Policy for UPDATE: Users can update their own company profile
CREATE POLICY "Users can update their own company profile."
ON public.company_profiles FOR UPDATE
TO authenticated
USING ( (SELECT auth.uid()) = user_id )
WITH CHECK ( (SELECT auth.uid()) = user_id );

-- Policy for DELETE: Users can delete their own company profile
CREATE POLICY "Users can delete their own company profile."
ON public.company_profiles FOR DELETE
TO authenticated
USING ( (SELECT auth.uid()) = user_id );

-- Enable RLS on opening_hours
ALTER TABLE public.opening_hours ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Users can view opening hours for their company
CREATE POLICY "Users can view opening hours for their company."
ON public.opening_hours FOR SELECT
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for INSERT: Users can add opening hours for their company
CREATE POLICY "Users can add opening hours for their company."
ON public.opening_hours FOR INSERT
TO authenticated
WITH CHECK ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for UPDATE: Users can update opening hours for their company
CREATE POLICY "Users can update opening hours for their company."
ON public.opening_hours FOR UPDATE
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) )
WITH CHECK ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for DELETE: Users can delete opening hours for their company
CREATE POLICY "Users can delete opening hours for their company."
ON public.opening_hours FOR DELETE
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Enable RLS on blog_posts
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Users can view blog posts for their company
CREATE POLICY "Users can view blog posts for their company."
ON public.blog_posts FOR SELECT
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for INSERT: Users can create blog posts for their company
CREATE POLICY "Users can create blog posts for their company."
ON public.blog_posts FOR INSERT
TO authenticated
WITH CHECK ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for UPDATE: Users can update blog posts for their company
CREATE POLICY "Users can update blog posts for their company."
ON public.blog_posts FOR UPDATE
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) )
WITH CHECK ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for DELETE: Users can delete blog posts for their company
CREATE POLICY "Users can delete blog posts for their company."
ON public.blog_posts FOR DELETE
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Enable RLS on social_media_posts
ALTER TABLE public.social_media_posts ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Users can view social media posts for their company
CREATE POLICY "Users can view social media posts for their company."
ON public.social_media_posts FOR SELECT
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for INSERT: Users can create social media posts for their company
CREATE POLICY "Users can create social media posts for their company."
ON public.social_media_posts FOR INSERT
TO authenticated
WITH CHECK ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for UPDATE: Users can update social media posts for their company
CREATE POLICY "Users can update social media posts for their company."
ON public.social_media_posts FOR UPDATE
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) )
WITH CHECK ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Policy for DELETE: Users can delete social media posts for their company
CREATE POLICY "Users can delete social media posts for their company."
ON public.social_media_posts FOR DELETE
TO authenticated
USING ( company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid())) );

-- Enable RLS on images
ALTER TABLE public.images ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Users can view images associated with their company's social media posts
CREATE POLICY "Users can view images for their company."
ON public.images FOR SELECT
TO authenticated
USING (
    id IN (
        SELECT smi.image_id
        FROM public.social_media_posts_images smi
        JOIN public.social_media_posts smp ON smi.social_media_post_id = smp.id
        WHERE smp.company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid()))
    )
);

-- Policy for INSERT: Users can insert images associated with their company's social media posts
CREATE POLICY "Users can insert images for their company."
ON public.images FOR INSERT
TO authenticated
WITH CHECK (
    id IN (
        SELECT smi.image_id
        FROM public.social_media_posts_images smi
        JOIN public.social_media_posts smp ON smi.social_media_post_id = smp.id
        WHERE smp.company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid()))
    )
);

-- Policy for UPDATE: Users can update images associated with their company's social media posts
CREATE POLICY "Users can update images for their company."
ON public.images FOR UPDATE
TO authenticated
USING (
    id IN (
        SELECT smi.image_id
        FROM public.social_media_posts_images smi
        JOIN public.social_media_posts smp ON smi.social_media_post_id = smp.id
        WHERE smp.company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid()))
    )
)
WITH CHECK (
    id IN (
        SELECT smi.image_id
        FROM public.social_media_posts_images smi
        JOIN public.social_media_posts smp ON smi.social_media_post_id = smp.id
        WHERE smp.company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid()))
    )
);

-- Policy for DELETE: Users can delete images for their company.
CREATE POLICY "Users can delete images for their company."
ON public.images FOR DELETE
TO authenticated
USING (
    id IN (
        SELECT smi.image_id
        FROM public.social_media_posts_images smi
        JOIN public.social_media_posts smp ON smi.social_media_post_id = smp.id
        WHERE smp.company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid()))
    )
);

-- Enable RLS on social_media_posts_images
ALTER TABLE public.social_media_posts_images ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Users can view social media posts images for their company
CREATE POLICY "Users can view social media posts images for their company."
ON public.social_media_posts_images FOR SELECT
TO authenticated
USING ( social_media_post_id IN (SELECT id FROM public.social_media_posts WHERE company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid()))) );

-- Policy for INSERT: Users can add social_media_posts_images for their company
CREATE POLICY "Users can add social media posts images for their company."
ON public.social_media_posts_images FOR INSERT
TO authenticated
WITH CHECK ( social_media_post_id IN (SELECT id FROM public.social_media_posts WHERE company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid()))) );

-- Policy for DELETE: Users can delete social_media_posts_images for their company
CREATE POLICY "Users can delete social media posts images for their company."
ON public.social_media_posts_images FOR DELETE
TO authenticated
USING ( social_media_post_id IN (SELECT id FROM public.social_media_posts WHERE company_profile_id IN (SELECT id FROM public.company_profiles WHERE user_id = (SELECT auth.uid()))) );