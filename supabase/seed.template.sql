-- Seed Data Template
-- Variables will be replaced by sed command
-- USER_UUID_VAR = placeholder for user UUID
-- COMPANY_NAME_VAR = placeholder for company name

DO $$
DECLARE
    user_uuid_val UUID := 'USER_UUID_VAR'::UUID;
    company_name_val TEXT := 'COMPANY_NAME_VAR';
    company_profile_id UUID;
BEGIN
    -- Temporarily disable RLS policies to allow direct inserts
    ALTER TABLE public.company_profiles DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.opening_hours DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.blog_posts DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.social_media_posts DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.images DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.social_media_posts_images DISABLE ROW LEVEL SECURITY;

    -- Insert Company Profile and capture the generated ID
    INSERT INTO public.company_profiles (
        user_id,
        business_name,
        address_line1,
        address_line2,
        city,
        state,
        postal_code,
        country,
        phone_number,
        email,
        website,
        company_type,
        logo_url,
        created_at,
        updated_at
    ) VALUES (
        user_uuid_val,
        company_name_val,
        '123 Beauty Boulevard',
        'Suite 200',
        'Amsterdam',
        'NH',
        '1012AB',
        'Netherlands',
        '+31 20 123 4567',
        'info@' || company_name_val || '.nl',
        'https://www.' || company_name_val || '.nl',
        'Beauty Salon',
        'https://example.com/assets/' || company_name_val || '-logo.png',
        NOW(),
        NOW()
    ) RETURNING id INTO company_profile_id;

    -- Insert Opening Hours (Monday to Saturday)
    INSERT INTO public.opening_hours (company_profile_id, day_of_week, start_time, end_time) VALUES
    (company_profile_id, 1, '09:00:00', '18:00:00'), -- Monday
    (company_profile_id, 2, '09:00:00', '18:00:00'), -- Tuesday
    (company_profile_id, 3, '09:00:00', '18:00:00'), -- Wednesday
    (company_profile_id, 4, '09:00:00', '20:00:00'), -- Thursday (late night)
    (company_profile_id, 5, '09:00:00', '18:00:00'), -- Friday
    (company_profile_id, 6, '10:00:00', '16:00:00'); -- Saturday

    -- Insert Blog Posts
    INSERT INTO public.blog_posts (company_profile_id, title, body, created_at, updated_at, published_at) VALUES
    (
        company_profile_id,
        'Welcome to ' || company_name_val || ' - Your Premier Beauty Destination',
        'We are thrilled to welcome you to ' || company_name_val || ', where beauty meets expertise. Our team of skilled professionals is dedicated to providing you with exceptional beauty services in a relaxing and luxurious environment. From cutting-edge hair styling to rejuvenating spa treatments, we offer a comprehensive range of services designed to enhance your natural beauty and boost your confidence.',
        NOW() - INTERVAL '7 days',
        NOW() - INTERVAL '7 days',
        NOW() - INTERVAL '7 days'
    ),
    (
        company_profile_id,
        'Spring Beauty Trends 2025: What''s Hot at ' || company_name_val,
        'Spring is here, and it''s time to refresh your look! At ' || company_name_val || ', we''re excited to share the hottest beauty trends for 2025. This season is all about natural glowing skin, bold lip colors, and effortless waves. Our expert stylists have curated a collection of looks that will have you feeling confident and radiant. Book your appointment today to try these stunning new trends!',
        NOW() - INTERVAL '3 days',
        NOW() - INTERVAL '3 days',
        NOW() - INTERVAL '3 days'
    ),
    (
        company_profile_id,
        'Behind the Scenes: A Day at ' || company_name_val,
        'Ever wondered what goes on behind the scenes at ' || company_name_val || '? Join us for a glimpse into our daily operations, from our morning team meetings where we discuss the day''s appointments and special requests, to our evening wind-down where we prepare for tomorrow. Our team''s dedication to excellence and attention to detail is what makes every visit to ' || company_name_val || ' a memorable experience.',
        NOW() - INTERVAL '1 day',
        NOW() - INTERVAL '1 day',
        NOW() - INTERVAL '1 day'
    );

    -- Insert Social Media Posts and capture their IDs for linking to images
    WITH social_posts AS (
        INSERT INTO public.social_media_posts (company_profile_id, title, body, created_at, updated_at, published_at) VALUES
        (
            company_profile_id,
            'Transform Your Look Today! ✨',
            'Ready for a stunning transformation? Our expert team at ' || company_name_val || ' is here to bring out your natural beauty. Book your appointment now and discover why we''re Amsterdam''s favorite beauty destination! 💄✨ #BeautyTransformation #' || company_name_val || ' #Amsterdam #BeautySalon',
            NOW() - INTERVAL '2 days',
            NOW() - INTERVAL '2 days',
            NOW() - INTERVAL '2 days'
        ),
        (
            company_profile_id,
            'Client Spotlight: Amazing Results! 🌟',
            'We love seeing our clients leave with confidence and a smile! Today we''re featuring one of our amazing transformations. Thank you for trusting ' || company_name_val || ' with your beauty journey! 💫 #ClientSpotlight #Transformation #' || company_name_val || ' #HappyClient',
            NOW() - INTERVAL '1 day',
            NOW() - INTERVAL '1 day',
            NOW() - INTERVAL '1 day'
        ),
        (
            company_profile_id,
            'Weekend Special: 20% Off All Services! 🎉',
            'This weekend only! Treat yourself to our premium beauty services with 20% off all treatments. Perfect time to try that new look you''ve been thinking about! Call us or book online. #WeekendSpecial #' || company_name_val || ' #BeautyDeals #Amsterdam',
            NOW(),
            NOW(),
            NOW()
        )
        RETURNING id, title
    ),
    inserted_images AS (
        INSERT INTO public.images (url, alt_text, created_at, updated_at) VALUES
        ('https://example.com/images/' || company_name_val || '/transformation-before-after-1.jpg', 'Amazing hair transformation before and after at ' || company_name_val, NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days'),
        ('https://example.com/images/' || company_name_val || '/client-spotlight-makeup.jpg', 'Client showcasing beautiful makeup results from ' || company_name_val, NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day'),
        ('https://example.com/images/' || company_name_val || '/weekend-special-promo.jpg', 'Weekend special promotion banner for ' || company_name_val, NOW(), NOW()),
        ('https://example.com/images/' || company_name_val || '/salon-interior-luxury.jpg', 'Luxurious interior of ' || company_name_val || ' beauty salon', NOW(), NOW()),
        ('https://example.com/images/' || company_name_val || '/team-photo-2025.jpg', 'Professional team photo of ' || company_name_val || ' staff', NOW(), NOW())
        RETURNING id, alt_text
    )
    -- Link Social Media Posts to Images using a more dynamic approach
    INSERT INTO public.social_media_posts_images (social_media_post_id, image_id)
    SELECT
        sp.id,
        img.id
    FROM social_posts sp
    CROSS JOIN inserted_images img
    WHERE
        -- Transform Your Look post gets transformation and salon interior images
        (sp.title LIKE 'Transform Your Look%' AND (img.alt_text LIKE '%transformation%' OR img.alt_text LIKE '%interior%'))
        OR
        -- Client Spotlight post gets makeup and team photo images
        (sp.title LIKE 'Client Spotlight%' AND (img.alt_text LIKE '%makeup%' OR img.alt_text LIKE '%team photo%'))
        OR
        -- Weekend Special post gets promo banner
        (sp.title LIKE 'Weekend Special%' AND img.alt_text LIKE '%promo%');

    -- Re-enable RLS policies
    ALTER TABLE public.company_profiles ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.opening_hours ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.social_media_posts ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.images ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.social_media_posts_images ENABLE ROW LEVEL SECURITY;

    RAISE NOTICE 'Seed data successfully inserted for %!', company_name_val;
END $$;