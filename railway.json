{"$schema": "https://railway.app/railway.schema.json", "services": [{"name": "frontend", "path": "services/frontend", "build": {"builder": "DOCKERFILE", "buildCommand": "", "buildArgs": ["PUBLIC_SUPABASE_URL=${PUBLIC_SUPABASE_URL}", "PUBLIC_SUPABASE_ANON_KEY=${PUBLIC_SUPABASE_ANON_KEY}", "GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}", "GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}"]}, "deploy": {"startCommand": "node build/index.js", "healthcheckPath": "/health", "healthcheckTimeout": 30000, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}}, {"name": "ai-service", "path": "services/ai-service", "build": {"builder": "DOCKERFILE", "buildCommand": ""}, "deploy": {"startCommand": "uvicorn app:app --host 0.0.0.0 --port 8000", "healthcheckPath": "/", "healthcheckTimeout": 30000}}, {"name": "api-gateway", "path": "services/api-gateway", "build": {"builder": "DOCKERFILE", "buildCommand": ""}, "deploy": {"startCommand": "./main", "healthcheckPath": "/", "healthcheckTimeout": 30000}}]}