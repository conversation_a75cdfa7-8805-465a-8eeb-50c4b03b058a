# SalonIntel

SalonIntel is a comprehensive platform designed to revolutionize salon management. This monorepo contains the frontend, backend services (Python for AI, Golang for utilities), and Supabase for database management.

## Project Structure

*   `frontend/sveltekit`: SvelteKit 2.x application with Tailwind CSS and ShadcnUI.
*   `backend/python`: Python 3.10+ services for AI and business logic.
*   `backend/golang`: Golang 1.20+ utilities and microservices.
*   `supabase`: Supabase local development setup.

## Getting Started

### Prerequisites

*   Docker and Docker Compose
*   Node.js (for SvelteKit development)
*   Python 3.10+ (for backend development)
*   Go 1.20+ (for backend development)
*   Supabase CLI (install globally: `npm install -g supabase` or `brew install supabase/supabase/supabase`)

### Local Development

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/salonintel.git
    cd salonintel
    ```
2.  **Start Docker environments (PostgreSQL for Supabase, SvelteKit, Python, Golang):**
    ```bash
    docker-compose up --build -d
    ```
3.  **Initialize Supabase project and link to local database (if not already done):**
    ```bash
    cd supabase
    supabase init # Only if you haven't initialized a Supabase project here before
    supabase link --project-ref your-project-id --db-url postgresql://postgres:supabase_password@localhost:5432/postgres # Replace 'your-project-id' with your actual Supabase project ID if linking to a remote project, otherwise just use the local db-url
    supabase start # This will start the Supabase services and connect to the local PostgreSQL
    # Follow Supabase instructions to apply migrations if any
    cd ..
    ```
4.  **Install frontend dependencies and run dev server:**
    ```bash
    cd frontend/sveltekit
    npm install
    npm run dev
    ```
5.  **Run backend services:**
    *   **Python:**
        ```bash
        cd backend/python
        # Install dependencies and run your Python app
        ```
    *   **Golang:**
        ```bash
        cd backend/golang
        # Install dependencies and run your Go app
        ```

## Deployment

This project is configured for continuous deployment with Railway.app. Pushing to the `main` branch will trigger an automatic deployment.

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct, and the process for submitting pull requests to us.

## License

This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.