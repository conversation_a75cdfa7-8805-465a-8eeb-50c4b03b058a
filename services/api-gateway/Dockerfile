# Stage 1: Base for building and development
FROM golang:1.24-bookworm AS base

# Create a non-root user for security
RUN adduser \
  --disabled-password \
  --gecos "" \
  --home "/nonexistent" \
  --shell "/sbin/nologin" \
  --no-create-home \
  --uid 65532 \
  small-user

WORKDIR /app
COPY go.mod ./
RUN go mod tidy # Generates go.sum and downloads dependencies

# Stage 2: Builder for production binary
FROM base AS builder
COPY . .
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /main .

# Stage 3: Development with hot-reloading (using air)
FROM base AS development
RUN go install github.com/air-verse/air@latest
COPY . .
CMD ["air"]

# Stage 4: Production (scratch image)
FROM scratch
WORKDIR /app
# Copy necessary certificates and user info for a minimal image
COPY --from=base /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=base /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=base /etc/passwd /etc/passwd
COPY --from=base /etc/group /etc/group

# Copy the built binary from the builder stage
COPY --from=builder /main .

# Run as the non-root user
USER small-user:small-user

EXPOSE 8080
CMD ["./main"]