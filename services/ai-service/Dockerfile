# Stage 1: Builder
# Use python:3.12-slim-bookworm for a smaller base image and consistency
FROM python:3.12-slim-bookworm AS builder
WORKDIR /app

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Remove pip, setuptools, and wheel to reduce image size for production
# Adjust path for Python 3.12
RUN rm -rf /usr/local/lib/python3.12/site-packages/pip* \
           /usr/local/lib/python3.12/site-packages/setuptools* \
           /usr/local/lib/python3.12/site-packages/wheel*

# Copy the rest of the application code
COPY . .


# Stage 2: Development with hot-reloading
# Use a full Python image for development tools
FROM python:3.12-bookworm AS development
WORKDIR /app

# Copy requirements and install them for the development environment
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY . .

# Expose the port Flask will run on
EXPOSE 8001

# Command to run the Flask development server
CMD ["flask", "run", "--debug", "--host", "0.0.0.0", "--port", "8001"]


# Stage 3: Production (distroless, non-root)
# Switch to distroless+nonroot for our application container.
# This image already sets USER to nonroot and WORKDIR to /home/<USER>
FROM gcr.io/distroless/python3-debian12:nonroot

# Copy the installed packages from the builder stage
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages

# Set environment variable so Python can find the installed packages
ENV PYTHONPATH=/usr/local/lib/python3.12/site-packages

# Copy application code from the builder stage to /home/<USER>/app
# All application files will reside inside /home/<USER>/app
COPY --from=builder /app /home/<USER>/app

# Set the working directory for the application in the production stage
WORKDIR /home/<USER>/app

# Define the entrypoint to run the application
# Assuming your main application file is app.py located in the root of your app directory
ENTRYPOINT ["python3", "-m", "gunicorn", "-w", "4", "-b", "0.0.0.0:8001", "app:app"]
