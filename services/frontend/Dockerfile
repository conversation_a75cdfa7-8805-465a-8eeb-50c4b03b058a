# Stage 1: Base for dependencies
FROM node:lts-bookworm AS base
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci

# Stage 2: Builder for production build
FROM base AS builder
# These ARGs will be passed from the build command
ARG PUBLIC_SUPABASE_URL
ARG PUBLIC_SUPABASE_ANON_KEY
ARG GOOGLE_CLIENT_ID
ARG GOOGLE_CLIENT_SECRET

# Install adapter-node in the builder stage
RUN npm install @sveltejs/adapter-node@2.0.0

# Set as environment variables for the build process
ENV PUBLIC_SUPABASE_URL=${PUBLIC_SUPABASE_URL}
ENV PUBLIC_SUPABASE_ANON_KEY=${PUBLIC_SUPABASE_ANON_KEY}
ENV GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
ENV GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
ENV VITE_PUBLIC_SUPABASE_URL=${PUBLIC_SUPABASE_URL}
ENV VITE_PUBLIC_SUPABASE_ANON_KEY=${PUBLIC_SUPABASE_ANON_KEY}

COPY . .
RUN npm run build

# Stage 3: Development layer with hot-reloading
FROM base AS development
COPY . .
# Install adapter-node in the development stage
RUN npm install @sveltejs/adapter-node@2.0.0
ENV PUBLIC_SUPABASE_URL=${PUBLIC_SUPABASE_URL}
ENV PUBLIC_SUPABASE_ANON_KEY=${PUBLIC_SUPABASE_ANON_KEY}
ENV GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
ENV GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
ENV VITE_PUBLIC_SUPABASE_URL=${PUBLIC_SUPABASE_URL}
ENV VITE_PUBLIC_SUPABASE_ANON_KEY=${PUBLIC_SUPABASE_ANON_KEY}
ENV NODE_ENV=development
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

# Stage 4: Create the final distroless image
FROM gcr.io/distroless/nodejs22-debian12:nonroot
WORKDIR /home/<USER>/app

# Copy built app and dependencies
COPY --from=builder /app/build ./build
COPY --from=base /app/node_modules ./node_modules
COPY package.json ./

# Set environment variables for runtime
ENV NODE_ENV=production
ENV PORT=8080
# These are needed for SvelteKit's $env/static/public
ENV PUBLIC_SUPABASE_URL=${PUBLIC_SUPABASE_URL}
ENV PUBLIC_SUPABASE_ANON_KEY=${PUBLIC_SUPABASE_ANON_KEY}
ENV GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
ENV GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
# These are needed for Vite/runtime access
ENV VITE_PUBLIC_SUPABASE_URL=${PUBLIC_SUPABASE_URL}
ENV VITE_PUBLIC_SUPABASE_ANON_KEY=${PUBLIC_SUPABASE_ANON_KEY}

EXPOSE 8080
CMD ["build/index.js"]
