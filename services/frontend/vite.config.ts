import precompileIntl from 'svelte-intl-precompile/sveltekit-plugin';
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import dotenv from 'dotenv';

// Load environment variables from the root directory
dotenv.config({ path: resolve(__dirname, '../../.env') });

export default defineConfig({
	resolve: {
		dedupe: ['svelte']
	},
	plugins: [
		precompileIntl('locales'), // if your translations are defined in /locales/[lang].json
		sveltekit()
	],
	// Make sure Vite can find the .env file in the root directory
	envDir: resolve(__dirname, '../..')
});
