import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	// Check if user is authenticated
	const {
		data: { user },
		error: authError
	} = await locals.supabase.auth.getUser();

	if (authError || !user) {
		return {
			company_profiles: [],
			user: null,
			message: 'Please log in to view your company profiles'
		};
	}

	// Helper function to handle Supabase queries
	const fetchData = async (query: any, name: string) => {
		try {
			const { data, error } = await query;
			if (error) throw error;
			return data ?? [];
		} catch (error) {
			console.error(`Error fetching ${name}:`, error);
			return [];
		}
	};

	// Fetch user's company profiles first
	const company_profiles = await fetchData(
		locals.supabase.from('company_profiles').select('*'),
		'company profiles'
	);

	// If no company profiles, return early
	if (!company_profiles.length) {
		return {
			company_profiles: [],
			blog_posts: [],
			opening_hours: [],
			social_media_posts: [],
			user,
			message: 'No company profiles found. Create one to get started!'
		};
	}

	// Get the company profile IDs for filtering related data
	const companyIds = company_profiles.map((cp: any) => cp.id);

	// Fetch related data concurrently using the correct field names
	const [blog_posts, opening_hours, social_media_posts] = await Promise.all([
		fetchData(
			locals.supabase
				.from('blog_posts')
				.select('*')
				.in('company_profile_id', companyIds),
			'blog posts'
		),
		fetchData(
			locals.supabase
				.from('opening_hours')
				.select('*')
				.in('company_profile_id', companyIds)
				.order('day_of_week'),
			'opening hours'
		),
		fetchData(
			locals.supabase
				.from('social_media_posts')
				.select('*')
				.in('company_profile_id', companyIds),
			'social media posts'
		)
	]);

	return {
		company_profiles,
		blog_posts,
		opening_hours,
		social_media_posts,
		user,
		message: null
	};
};
