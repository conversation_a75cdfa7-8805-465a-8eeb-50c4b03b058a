<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button } from '$lib/components/ui/button';

	let { data } = $props();

	// Helper function to convert day number to day name
	const getDayName = (dayNum: number): string => {
		const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
		return days[dayNum] || 'Unknown';
	};

	// Helper function to format time
	const formatTime = (time: string): string => {
		if (!time) return '';
		return new Date(`1970-01-01T${time}`).toLocaleTimeString([], {
			hour: '2-digit',
			minute: '2-digit'
		});
	};
</script>

<div class="min-h-screen bg-gray-50 py-8">
	<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
			<p class="mt-2 text-gray-600">Manage your business profile and content</p>
		</div>

		<!-- User Info -->
		{#if data.user}
			<div class="mb-6 rounded-lg bg-green-50 p-4 border-l-4 border-green-400">
				<p class="text-sm text-green-700">
					<span class="font-medium">Logged in as:</span> {data.user.email}
				</p>
			</div>
		{/if}

		<!-- Message -->
		{#if data.message}
			<div class="mb-6 rounded-lg p-4 border-l-4 {data.user ? 'bg-blue-50 border-blue-400 text-blue-700' : 'bg-yellow-50 border-yellow-400 text-yellow-700'}">
				{data.message}
			</div>
		{/if}

		<!-- Main Content Grid -->
		<div class="grid gap-8 lg:grid-cols-2">
			<!-- Company Profiles Section -->
			<div class="lg:col-span-2">
				<h2 class="text-xl font-semibold text-gray-900 mb-4">Company Profiles</h2>
				{#if data.company_profiles.length > 0}
					<div class="grid gap-6 md:grid-cols-2">
						{#each data.company_profiles as company}
							<div class="rounded-lg bg-white p-6 shadow-sm border border-gray-200">
								<div class="flex items-start justify-between mb-4">
									<div>
										<h3 class="text-lg font-medium text-gray-900">{company.business_name}</h3>
										<span class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
											{company.company_type || 'Business'}
										</span>
									</div>
								</div>
								
								<div class="space-y-4">
									<!-- Address -->
									{#if company.address_line1}
										<div>
											<h4 class="text-sm font-medium text-gray-700 mb-1">Address</h4>
											<p class="text-sm text-gray-600">
												{company.address_line1}
												{#if company.address_line2}<br>{company.address_line2}{/if}
												{#if company.city}<br>{company.city}{#if company.state}, {company.state}{/if} {company.postal_code}{/if}
												{#if company.country}<br>{company.country}{/if}
											</p>
										</div>
									{/if}

									<!-- Contact -->
									<div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
										{#if company.phone_number}
											<div>
												<h4 class="text-sm font-medium text-gray-700">Phone</h4>
												<p class="text-sm text-gray-600">{company.phone_number}</p>
											</div>
										{/if}
										{#if company.email}
											<div>
												<h4 class="text-sm font-medium text-gray-700">Email</h4>
												<p class="text-sm text-gray-600">{company.email}</p>
											</div>
										{/if}
									</div>

									{#if company.website}
										<div>
											<h4 class="text-sm font-medium text-gray-700">Website</h4>
											<a href="{company.website}" target="_blank" class="text-sm text-blue-600 hover:text-blue-800 hover:underline">
												{company.website}
											</a>
										</div>
									{/if}
								</div>
							</div>
						{/each}
					</div>
				{:else if data.user}
					<div class="rounded-lg bg-gray-50 p-6 text-center">
						<p class="text-gray-500 mb-2">No company profiles found</p>
						<p class="text-sm text-gray-400">Create your first company profile to get started</p>
					</div>
				{/if}
			</div>

			<!-- Opening Hours Section -->
			<div>
				<h2 class="text-xl font-semibold text-gray-900 mb-4">Opening Hours</h2>
				{#if data.opening_hours && data.opening_hours.length > 0}
					<div class="rounded-lg bg-white shadow-sm border border-gray-200">
						{#each data.opening_hours as hours, index}
							<div class="p-4 {index !== data.opening_hours.length - 1 ? 'border-b border-gray-200' : ''}">
								<div class="flex justify-between items-center">
									<span class="font-medium text-gray-900">{getDayName(hours.day_of_week)}</span>
									<span class="text-gray-600">
										{formatTime(hours.start_time)} - {formatTime(hours.end_time)}
									</span>
								</div>
							</div>
						{/each}
					</div>
				{:else if data.user}
					<div class="rounded-lg bg-gray-50 p-6 text-center">
						<p class="text-gray-500">No opening hours set</p>
					</div>
				{/if}
			</div>

			<!-- Blog Posts Section -->
			<div>
				<h2 class="text-xl font-semibold text-gray-900 mb-4">Blog Posts</h2>
				{#if data.blog_posts && data.blog_posts.length > 0}
					<div class="space-y-4">
						{#each data.blog_posts as post}
							<div class="rounded-lg bg-white p-4 shadow-sm border border-gray-200">
								<h3 class="font-medium text-gray-900 mb-2">{post.title}</h3>
								{#if post.body}
									<p class="text-sm text-gray-600 mb-3 line-clamp-3">{post.body.slice(0, 150)}...</p>
								{/if}
								<div class="flex justify-between items-center text-xs text-gray-500">
									<span>
										{#if post.published_at}
											Published {new Date(post.published_at).toLocaleDateString()}
										{:else}
											Updated {new Date(post.updated_at).toLocaleDateString()}
										{/if}
									</span>
									<span class="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600">
										{post.published_at ? 'Published' : 'Draft'}
									</span>
								</div>
							</div>
						{/each}
					</div>
				{:else if data.user}
					<div class="rounded-lg bg-gray-50 p-6 text-center">
						<p class="text-gray-500">No blog posts yet</p>
					</div>
				{/if}
			</div>

			<!-- Social Media Posts Section -->
			<div class="lg:col-span-2">
				<h2 class="text-xl font-semibold text-gray-900 mb-4">Social Media Posts</h2>
				{#if data.social_media_posts && data.social_media_posts.length > 0}
					<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
						{#each data.social_media_posts as post}
							<div class="rounded-lg bg-white p-4 shadow-sm border border-gray-200">
								<h3 class="font-medium text-gray-900 mb-2">{post.title}</h3>
								{#if post.body}
									<p class="text-sm text-gray-600 mb-3">{post.body.slice(0, 100)}...</p>
								{/if}
								<div class="text-xs text-gray-500">
									{#if post.published_at}
										Published {new Date(post.published_at).toLocaleDateString()}
									{:else}
										Updated {new Date(post.updated_at).toLocaleDateString()}
									{/if}
								</div>
							</div>
						{/each}
					</div>
				{:else if data.user}
					<div class="rounded-lg bg-gray-50 p-6 text-center">
						<p class="text-gray-500">No social media posts yet</p>
					</div>
				{/if}
			</div>
		</div>

		<!-- Logout Button -->
		<div class="mt-8 pt-6 border-t border-gray-200">
			<form method="POST" action="/logout?/logout" use:enhance>
				<Button type="submit" class="bg-red-600 hover:bg-red-700">Logout</Button>
			</form>
		</div>
	</div>
</div>
