<script lang="ts">
	import OnboardingFlow from '$lib/components/organisms/OnboardingFlow.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	function handleOnboardingComplete(event: CustomEvent) {
		const onboardingData = event.detail;
		console.log('Onboarding completed with data:', onboardingData);
		console.log('User:', data.user);

		// Here you would typically:
		// 1. Save the data to your backend/database
		// 2. Redirect to dashboard or next page
		// 3. Show success message

		// For now, just log the data
		alert('Onboarding completed! Check console for data.');
	}
</script>

<svelte:head>
	<title>Setup Your Salon - SalonIntel</title>
	<meta name="description" content="Complete your salon setup to start using SalonIntel's AI marketing assistant." />
</svelte:head>

<div class="min-h-screen bg-gray-50">
	<OnboardingFlow on:complete={handleOnboardingComplete} />
</div>
