<script lang="ts">
	import OnboardingFlow from '$lib/components/organisms/OnboardingFlow.svelte';

	function handleOnboardingComplete(event: CustomEvent) {
		const onboardingData = event.detail;
		console.log('Onboarding completed with data:', onboardingData);
		
		// Here you would typically:
		// 1. Save the data to your backend/database
		// 2. Redirect to dashboard or next page
		// 3. Show success message
		
		// For now, just log the data
		alert('Onboarding completed! Check console for data.');
	}
</script>

<div class="min-h-screen bg-gray-50 py-8">
	<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
		<OnboardingFlow on:complete={handleOnboardingComplete} />
	</div>
</div>
