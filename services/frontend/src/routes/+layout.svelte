<script lang="ts">
	import { init, register, getLocaleFromNavigator, waitLocale } from 'svelte-intl-precompile';
	import { browser } from '$app/environment';
	import en from '../locales/en.json';
	import nl from '../locales/nl.json';
	import { invalidate } from '$app/navigation';
	import { onMount } from 'svelte';
	import '../app.css';

	register('en', () => Promise.resolve(en));
	register('nl', () => Promise.resolve(nl));

	const initialLocale = browser
		? localStorage.getItem('lang') || getLocaleFromNavigator() || 'en'
		: 'en';

	init({
		initialLocale,
		fallbackLocale: 'en'
	});

	let { data, children } = $props();
	let { session, supabase } = $derived(data);

	onMount(() => {
		const { data } = supabase.auth.onAuthStateChange((_, newSession) => {
			if (newSession?.expires_at !== session?.expires_at) {
				invalidate('supabase:auth');
			}
		});

		return () => data.subscription.unsubscribe();
	});
</script>

{@render children()}
