import { redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url, locals: { supabase } }) => {
  const code = url.searchParams.get('code');
  const token_hash = url.searchParams.get('token_hash');
  const type = url.searchParams.get('type');
  const next = url.searchParams.get('next') ?? '/private';

  if (code) {
    // OAuth callback
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    if (error) {
      throw redirect(303, '/auth/error');
    }
    throw redirect(303, next);
  } else if (token_hash && type) {
    // Magic Link / OTP callback
    const { error } = await supabase.auth.verifyOtp({ token_hash, type: type as 'email' });
    if (error) {
      throw redirect(303, '/auth/error');
    }
    throw redirect(303, next);
  }

  throw redirect(303, '/auth/error');
};