<script lang="ts">
  import { page } from '$app/stores';
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import { onMount } from 'svelte';

  let errorDetails: {
    error?: string;
    error_code?: string;
    error_description?: string;
  } = {};

  onMount(() => {
    if (typeof window !== 'undefined' && window.location.hash) {
      const hash = window.location.hash.substring(1); // Remove the '#'
      const params = new URLSearchParams(hash);

      errorDetails = {
        error: params.get('error') || undefined,
        error_code: params.get('error_code') || undefined,
        error_description: params.get('error_description') || undefined
      };
    }
  });
</script>

<div class="flex min-h-screen items-center justify-center bg-gray-100">
  <Card.Root class="w-full max-w-md">
    <Card.Header>
      <Card.Title class="text-center text-2xl font-bold">Authentication Error</Card.Title>
    </Card.Header>
    <Card.Content>
      <p class="mb-4 text-red-500">{$page.status}: {$page.error?.message}</p>
      {#if errorDetails.error || errorDetails.error_code || errorDetails.error_description}
        <div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <h3 class="font-bold mb-2">Error Details from URL:</h3>
          {#if errorDetails.error}
            <p><strong>Error:</strong> {errorDetails.error}</p>
          {/if}
          {#if errorDetails.error_code}
            <p><strong>Code:</strong> {errorDetails.error_code}</p>
          {/if}
          {#if errorDetails.error_description}
            <p><strong>Description:</strong> {errorDetails.error_description}</p>
          {/if}
        </div>
      {/if}
      <Button href="/auth" class="w-full">Go to Login</Button>
    </Card.Content>
  </Card.Root>
</div>