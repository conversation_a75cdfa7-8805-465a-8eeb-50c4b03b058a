<script lang="ts">
	import { t } from 'svelte-intl-precompile';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card'; // Import shadcn-svelte Card
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { enhance } from '$app/forms';
	import type { SubmitFunction } from '@sveltejs/kit';
	import type { User } from '@supabase/supabase-js';
	import Icon from '@iconify/svelte';

	let loading = false;
	let errorMessage = '';

	// Check session on page load
	$: if ($page.data.supabase) {
		$page.data.supabase.auth
			.getUser()
			.then(({ data: { user } }: { data: { user: User | null } }) => {
				if (user) {
					goto('/account');
				}
			});
	}

	/**
	 * Handles the authentication submission.
	 * @returns A function that processes the result of the form submission.
	 */
	const handleAuthSubmit: SubmitFunction = () => {
		loading = true;
		errorMessage = '';
		return async ({ result }) => {
			if (result.type === 'error') {
				errorMessage = result.error?.message || 'An unknown error occurred.';
			} else if (result.type === 'redirect') {
				window.location.href = result.location; // Use window.location for external URLs
			} else if (result.type === 'success') {
				// If it's a success but no redirect, it means the session might have been set
				// but the page didn't refresh. We can force a refresh or redirect to /private
				// if a session is now available.
				if ($page.data.supabase) {
					$page.data.supabase.auth
						.getUser()
						.then(({ data: { user } }: { data: { user: User | null } }) => {
							if (user) {
								goto('/private');
							}
						});
				}
			}
			loading = false;
		};
	};
</script>

<div class="flex min-h-screen items-center justify-center bg-gray-100">
	<Card.Root class="w-full max-w-sm">
		<Card.Header>
			<Card.Title class="text-2xl">{$t('auth.login')}</Card.Title>
			<Card.Description>{$t('auth.loginDescription')}</Card.Description>
		</Card.Header>
		<Card.Content class="grid gap-4">
			{#if errorMessage}
				<p class="mb-4 text-red-500">{errorMessage}</p>
			{/if}

			<form method="POST" action="?/email" use:enhance={handleAuthSubmit} class="grid gap-4">
				<div class="grid gap-2">
					<Label for="email">{$t('auth.emailLabel')}</Label>
					<Input
						id="email"
						name="email"
						type="email"
						placeholder={$t('auth.emailPlaceholder')}
						required
					/>
				</div>
				<Button type="submit" class="w-full" disabled={loading}>
					{#if loading}
						{$t('auth.sendingMagicLink')}
					{:else}
						{$t('auth.signInWithMagicLink')}
					{/if}
				</Button>
			</form>

			<div class="relative my-4">
				<div class="absolute inset-0 flex items-center">
					<span class="w-full border-t"></span>
				</div>
				<div class="relative flex justify-center text-xs uppercase">
					<span class="bg-background px-2 text-muted-foreground">{$t('auth.orContinueWith')}</span>
				</div>
			</div>

			<form method="POST" action="?/google" use:enhance={handleAuthSubmit}>
				<Button
					type="submit"
					class="flex w-full items-center justify-center space-x-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
					disabled={loading}
				>
					{#if loading}
						{$t('auth.signingInWithGoogle')}
					{:else}
						<svg
							class="h-5 w-5"
							viewBox="0 0 24 24"
							fill="currentColor"
							xmlns="http://www.w3.org/2000/svg"
						>
							<path
								d="M12.0003 4.75C14.0253 4.75 15.8003 5.4625 17.1503 6.7875L20.0253 3.9125C18.0753 2.075 15.2503 1 12.0003 1C7.72533 1 3.95033 3.45 2.05033 7.05L5.90033 9.925C6.85033 7.075 9.25033 4.75 12.0003 4.75Z"
								fill="#EA4335"
							/>
							<path
								d="M23.0003 12.0001C23.0003 11.3251 22.9003 10.6501 22.7753 10.0001H12.0003V14.0001H18.4753C18.1753 15.6251 17.2253 17.0001 15.9003 17.9501L19.7503 20.8251C21.9003 18.8001 23.0003 15.8001 23.0003 12.0001Z"
								fill="#4285F4"
							/>
							<path
								d="M5.90033 9.92505C5.62533 10.5751 5.47533 11.2751 5.47533 12.0001C5.47533 12.7251 5.62533 13.4251 5.90033 14.0751L2.05033 17.9251C1.35033 16.5751 1.00033 15.0001 1.00033 12.0001C1.00033 9.00005 1.35033 7.42505 2.05033 6.07505L5.90033 9.92505Z"
								fill="#FBBC05"
							/>
							<path
								d="M12.0003 23.0001C15.2503 23.0001 18.0753 21.9251 20.0253 19.9251L15.9003 17.9501C14.8503 18.6751 13.5003 19.1751 12.0003 19.1751C9.25033 19.1751 6.85033 16.8501 5.90033 14.0751L2.05033 17.9251C3.95033 21.5251 7.72533 23.0001 12.0003 23.0001Z"
								fill="#34A853"
							/>
						</svg>
						{$t('auth.signInWithGoogle')}
					{/if}
				</Button>
			</form>
		</Card.Content>
	</Card.Root>
</div>
