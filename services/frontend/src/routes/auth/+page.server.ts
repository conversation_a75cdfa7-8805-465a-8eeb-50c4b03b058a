import { AuthApiError } from '@supabase/supabase-js';
import { fail, redirect } from '@sveltejs/kit';

// Get the callback URL based on the environment
function getCallbackUrl(url: URL): string {
	if (process.env.NODE_ENV === 'production') {
		// For production on Railway
		// Use the RAILWAY_PUBLIC_DOMAIN environment variable for the production URL
		const railwayPublicDomain = process.env.RAILWAY_PUBLIC_DOMAIN;
		if (!railwayPublicDomain) {
			// This should ideally not happen if Railway env vars are set correctly
			// If it does, it means the env var is not propagated, and we should fail or use a known default
			throw new Error('RAILWAY_PUBLIC_DOMAIN environment variable is not set in production.');
		}
		return `https://${railwayPublicDomain}/auth/callback`;
	} else {
		// For development environments
		if (process.env.DOCKER_ENVIRONMENT === 'true') {
			// For local development with Docker
			return `${url.origin.replace('host.docker.internal', 'localhost')}/auth/callback`;
		}
		// Default development fallback (non-Docker)
		return `${url.origin}/auth/callback`;
	}
}

export const actions = {
	google: async ({ url, locals: { supabase } }) => {
		// Get the correct callback URL for this environment
		const callbackUrl = getCallbackUrl(url);

		const { data, error } = await supabase.auth.signInWithOAuth({
			provider: 'google',
			options: {
				redirectTo: callbackUrl
			}
		});

		if (error) {
			return fail(500, {
				message: 'Something went wrong during Google sign-in.',
				error: (error as any).message || 'Unknown error' // Keep the safe access
			});
		}

		// For Docker environment, replace host.docker.internal with localhost in the URL
		// This is for the initial redirect to Supabase's auth endpoint, which the browser needs to resolve
		let redirectUrl = data.url;
		if (process.env.DOCKER_ENVIRONMENT === 'true') {
			redirectUrl = redirectUrl.replace('host.docker.internal', 'localhost');
		}

		throw redirect(303, redirectUrl);
	},
	email: async ({ request, url, locals: { supabase } }) => {
		const formData = await request.formData();
		const email = formData.get('email') as string;

		if (!email) {
			return fail(400, {
				message: 'Email is required.',
				email
			});
		}

		// Get the correct callback URL for this environment
		const callbackUrl = getCallbackUrl(url);

		// For Docker environment, we need to ensure the email redirect uses localhost
		// This is only for the email OTP redirect, not for Google OAuth
		let emailRedirectTo = callbackUrl;
		if (process.env.DOCKER_ENVIRONMENT === 'true') {
			// Make sure it's using localhost, not host.docker.internal
			emailRedirectTo = emailRedirectTo.replace('host.docker.internal', 'localhost');
		}

		const { error } = await supabase.auth.signInWithOtp({
			email,
			options: {
				emailRedirectTo
			}
		});

		if (error) {
			if (error instanceof AuthApiError && error.status === 400) {
				return fail(400, {
					message: 'Invalid credentials.',
					email
				});
			}
			return fail(500, {
				message: 'Something went wrong. Please try again later.',
				email
			});
		}

		return {
			success: true,
			message: 'Magic link sent! Check your email for the login link.'
		};
	}
};
