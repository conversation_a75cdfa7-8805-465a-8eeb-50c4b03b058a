import type { Session, SupabaseClient, User } from '@supabase/supabase-js';
import type { Database } from './database.types.ts'; // import generated types

declare module 'svelte-intl-precompile' {
export type TranslationKey = 
  | 'auth.login'
  | 'auth.loginDescription',
	'auth.emailLabel'
  | 'auth.emailPlaceholder'
  | 'auth.sendingMagicLink'
  | 'auth.signInWithMagicLink'
  | 'auth.orContinueWith'
  | 'auth.signingInWithGoogle'
  | 'auth.signInWithGoogle'
  | 'auth.unknownError';
}

declare global {
	namespace App {
		interface Locals {
			supabase: SupabaseClient<Database>;
			safeGetSession: () => Promise<{ session: Session | null; user: User | null }>;
			session: Session | null;
			user: User | null;
		}
		interface PageData {
			session: Session | null;
		}
		// interface Error {}
		// interface Platform {}
	}
}

export {};
