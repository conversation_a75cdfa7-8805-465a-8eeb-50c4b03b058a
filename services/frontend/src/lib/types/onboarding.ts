export interface OnboardingData {
	businessInfo: {
		salonName: string;
		streetAddress: string;
		city: string;
		postalCode: string;
		country: string;
		phoneNumber: string;
		email: string;
		website?: string;
		logo?: string;
		companyType: 'Beauty Salon' | 'Hair Salon' | 'Nail Salon';
	};
	services: {
		categories: string[];
		pricingTier: 'budget' | 'mid-range' | 'premium';
		mission?: string;
	};
	socialMedia: {
		instagram?: string;
		facebook?: string;
		tiktok?: string;
		linkedin?: string;
	};
	goals: {
		objectives: string[];
		priority: string;
	};
}

export interface ValidationErrors {
	[key: string]: string;
}

export type OnboardingStep = 1 | 2 | 3 | 4 | 5 | 6;
