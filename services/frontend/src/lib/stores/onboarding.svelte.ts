import type { OnboardingData, ValidationErrors, OnboardingStep } from '$lib/types/onboarding';

const initialData: OnboardingData = {
	businessInfo: {
		salonName: '',
		streetAddress: '',
		city: '',
		postalCode: '',
		phone: '',
		email: '',
		website: ''
	},
	services: {
		categories: [],
		pricingTier: 'mid-range',
		mission: ''
	},
	socialMedia: {
		instagram: '',
		facebook: '',
		tiktok: '',
		linkedin: ''
	},
	goals: {
		objectives: [],
		priority: ''
	}
};

class OnboardingStore {
	currentStep = $state<OnboardingStep>(1);
	data = $state<OnboardingData>(structuredClone(initialData));
	errors = $state<ValidationErrors>({});
	isSubmitting = $state(false);

	updateData(section: keyof OnboardingData, updates: any) {
		this.data[section] = { ...this.data[section], ...updates };

		// Clear errors for updated fields
		Object.keys(updates).forEach((key) => {
			delete this.errors[key];
		});
	}

	validateStep(step: OnboardingStep): boolean {
		const newErrors: ValidationErrors = {};

		switch (step) {
			case 2:
				if (!this.data.businessInfo.salonName.trim())
					newErrors.salonName = 'Salon name is required';
				if (!this.data.businessInfo.streetAddress.trim())
					newErrors.streetAddress = 'Street address is required';
				if (!this.data.businessInfo.city.trim()) newErrors.city = 'City is required';
				if (!this.data.businessInfo.postalCode.trim())
					newErrors.postalCode = 'Postal code is required';
				if (!this.data.businessInfo.phone.trim()) newErrors.phone = 'Phone number is required';
				if (!this.data.businessInfo.email.trim()) {
					newErrors.email = 'Email is required';
				} else if (!/\S+@\S+\.\S+/.test(this.data.businessInfo.email)) {
					newErrors.email = 'Please enter a valid email address';
				}
				// website is optional, only validate if provided
				if (
					this.data.businessInfo.website &&
					!/^(ftp|http|https):\/\/[^ "]+$/.test(this.data.businessInfo.website)
				) {
					newErrors.website = 'Please enter a valid website URL';
				}
				break;
			case 3:
				if (this.data.services.categories.length === 0) {
					newErrors.categories = 'Please select at least one service';
				}
				break;
			case 5:
				if (this.data.goals.objectives.length === 0) {
					newErrors.objectives = 'Please select at least one marketing goal';
				}
				break;
		}

		this.errors = newErrors;
		return Object.keys(newErrors).length === 0;
	}

	nextStep() {
		if (this.validateStep(this.currentStep)) {
			this.currentStep = Math.min(this.currentStep + 1, 6) as OnboardingStep;
		}
	}

	prevStep() {
		this.currentStep = Math.max(this.currentStep - 1, 1) as OnboardingStep;
	}

	goToStep(step: OnboardingStep) {
		this.currentStep = step;
	}

	async submitOnboarding() {
		this.isSubmitting = true;
		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 2000));
			console.log('Onboarding completed:', this.data);
			// Handle success (redirect, show confirmation, etc.)
		} catch (error) {
			console.error('Onboarding submission failed:', error);
		} finally {
			this.isSubmitting = false;
		}
	}

	reset() {
		this.currentStep = 1;
		this.data = structuredClone(initialData);
		this.errors = {};
		this.isSubmitting = false;
	}
}

export const onboardingStore = new OnboardingStore();
