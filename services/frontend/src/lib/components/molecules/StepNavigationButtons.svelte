<script lang="ts">
	import { ChevronLeft } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';
	import Button from '$lib/components/ui/button/button.svelte';
	import type { OnboardingStep } from '$lib/types/onboarding';

	export let currentStep: OnboardingStep;
	export let totalSteps: OnboardingStep;
	export let nextLabel: string = 'Continue';
	export let nextDisabled: boolean = false;
	export let isSubmitting: boolean = false;

	const dispatch = createEventDispatcher();

	// Computed properties for button visibility
	$: isFirstStep = currentStep === 1;
	$: isLastStep = currentStep === totalSteps;
	$: isMiddleStep = currentStep > 1 && currentStep < totalSteps;
	
	// Computed label for the button
	$: buttonLabel = isFirstStep ? 'Start' : nextLabel;

	function handleBack() {
		dispatch('back');
	}

	function handleNext() {
		dispatch('next');
	}
</script>

<div class="mt-8 border-t border-slate-100 pt-6">
	{#if isFirstStep}
		<!-- First step: centered button -->
		<div class="flex justify-center">
			<Button
				on:click={handleNext}
				disabled={nextDisabled || isSubmitting}
				size="lg"
			>
				{#if isSubmitting}
					<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
					Processing...
				{:else}
					{buttonLabel}
				{/if}
			</Button>
		</div>
	{:else if isMiddleStep}
		<!-- Middle steps: back and next buttons -->
		<div class="flex items-center justify-between">
			<Button
				on:click={handleBack}
				disabled={isSubmitting}
				variant="outline"
			>
				<ChevronLeft class="mr-2 h-4 w-4" />
				Back
			</Button>
			
			<Button
				on:click={handleNext}
				disabled={nextDisabled || isSubmitting}
			>
				{#if isSubmitting}
					<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
					Processing...
				{:else}
					{buttonLabel}
				{/if}
			</Button>
		</div>
	{:else if isLastStep}
		<!-- Last step: back button and finish button on the right -->
		<div class="flex items-center justify-between">
			<Button
				on:click={handleBack}
				disabled={isSubmitting}
				variant="outline"
			>
				<ChevronLeft class="mr-2 h-4 w-4" />
				Back
			</Button>
			
			<Button
				on:click={handleNext}
				disabled={nextDisabled || isSubmitting}
				size="lg"
			>
				{#if isSubmitting}
					<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
					Processing...
				{:else}
					{buttonLabel}
				{/if}
			</Button>
		</div>
	{/if}
</div>
