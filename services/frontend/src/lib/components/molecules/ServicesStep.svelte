<script lang="ts">
	import Checkbox from '$lib/components/ui/checkbox/checkbox.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	import { RadioGroup, RadioGroupItem } from '$lib/components/ui/radio-group';
	import Textarea from '$lib/components/ui/textarea/textarea.svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import type { OnboardingData } from '$lib/types/onboarding';

	// Props for consistency with other steps
	export let title: string;
	export let subtitle: string;
	export let icon: string;

	// Services data binding
	export let services: OnboardingData['services'];

	const availableServices = [
		'Hair Cut & Styling',
		'Hair Coloring',
		'Hair Extensions',
		'Manicure & Pedicure',
		'Facial Treatments',
		'Massage Therapy',
		'Eyebrow & Lash Services',
		'Waxing Services',
		'Makeup Services',
		'Bridal Services'
	];

	const pricingTiers = [
		{ value: 'budget', label: 'Budget-Friendly', description: 'Affordable services for everyone' },
		{ value: 'mid-range', label: 'Mid-Range', description: 'Quality services at fair prices' },
		{ value: 'premium', label: 'Premium', description: 'High-end luxury experience' }
	] as const;

	function toggleService(service: string) {
		if (services.categories.includes(service)) {
			services.categories = services.categories.filter(s => s !== service);
		} else {
			services.categories = [...services.categories, service];
		}
	}
</script>

<div class="space-y-8">
	<!-- Service Categories -->
	<Card>
		<CardHeader>
			<CardTitle class="text-lg">Service Categories</CardTitle>
			<p class="text-sm text-gray-600">Select all services you offer</p>
		</CardHeader>
		<CardContent>
			<div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
				{#each availableServices as service}
					<div class="flex items-center space-x-3">
						<Checkbox
							id={service}
							checked={services.categories.includes(service)}
							onCheckedChange={() => toggleService(service)}
						/>
						<Label for={service} class="text-sm font-medium text-gray-700">
							{service}
						</Label>
					</div>
				{/each}
			</div>
		</CardContent>
	</Card>

	<!-- Pricing Tier -->
	<Card>
		<CardHeader>
			<CardTitle class="text-lg">Pricing Tier</CardTitle>
			<p class="text-sm text-gray-600">How would you position your salon in the market?</p>
		</CardHeader>
		<CardContent>
			<RadioGroup bind:value={services.pricingTier} class="space-y-4">
				{#each pricingTiers as tier}
					<div class="flex items-start space-x-3">
						<RadioGroupItem value={tier.value} id={tier.value} class="mt-1" />
						<div class="space-y-1">
							<Label for={tier.value} class="text-sm font-medium">{tier.label}</Label>
							<p class="text-xs text-gray-500">{tier.description}</p>
						</div>
					</div>
				{/each}
			</RadioGroup>
		</CardContent>
	</Card>

	<!-- Business Mission -->
	<Card>
		<CardHeader>
			<CardTitle class="text-lg">Business Mission</CardTitle>
			<p class="text-sm text-gray-600">Tell us about your salon's mission and values (optional)</p>
		</CardHeader>
		<CardContent>
			<Textarea
				bind:value={services.mission}
				placeholder="Describe what makes your salon special, your values, and what you aim to achieve for your clients..."
				class="min-h-[100px] resize-none"
				maxlength={500}
			/>
			<p class="mt-2 text-xs text-gray-500">
				{services.mission?.length || 0}/500 characters
			</p>
		</CardContent>
	</Card>
</div>