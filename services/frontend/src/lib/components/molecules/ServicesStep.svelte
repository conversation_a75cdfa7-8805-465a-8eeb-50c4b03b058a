<script lang="ts">
	import Checkbox from '$lib/components/ui/checkbox/checkbox.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	
	export let selectedServices: string[] = [];
	
	const availableServices = [
		'Hair Cut & Styling',
		'Hair Coloring',
		'Manicure & Pedicure',
		'Facial Treatments',
		'Massage Therapy',
		'Eyebrow & Lash Services'
	];
	
	function toggleService(service: string) {
		if (selectedServices.includes(service)) {
			selectedServices = selectedServices.filter(s => s !== service);
		} else {
			selectedServices = [...selectedServices, service];
		}
	}
</script>

<div class="space-y-6">
	<div class="text-center">
		<h2 class="text-2xl font-bold text-gray-900">What services do you offer?</h2>
		<p class="mt-2 text-gray-600">Select all that apply to your business</p>
	</div>
	
	<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
		{#each availableServices as service}
			<div class="flex items-center space-x-3">
				<Checkbox
					id={service}
					checked={selectedServices.includes(service)}
					onCheckedChange={() => toggleService(service)}
				/>
				<Label for={service} class="text-sm font-medium text-gray-700">
					{service}
				</Label>
			</div>
		{/each}
	</div>
</div>