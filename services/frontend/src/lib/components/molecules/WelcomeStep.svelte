<script lang="ts">
	// Welcome step component
	import FeaturesGrid from '$lib/components/molecules/FeaturesGrid.svelte';

	// Props for consistency with other steps (hero section is now in StepLayout)
	export let title: string;
	export let subtitle: string;
	export let icon: string;
</script>

<div class="space-y-6">
	<FeaturesGrid />
	<div class="text-center">
		<p class="text-gray-700">
			We'll guide you through a quick setup to personalize your experience. This will only take a
			few minutes.
		</p>
	</div>
</div>
