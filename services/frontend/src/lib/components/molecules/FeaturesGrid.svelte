<script lang="ts">
	import FeatureCard from '$lib/components/molecules/FeatureCard.svelte';

	export let features = [
		{
			iconName: 'mdi:clock-outline',
			title: 'Quick Setup',
			description: 'Takes about 5 minutes to complete'
		},
		{
			iconName: 'mdi:shield-check',
			title: 'Secure & Private',
			description: 'Your data is protected and never shared'
		},
		{
			iconName: 'mdi:sparkles',
			title: 'Personalized Results',
			description: 'AI tailored specifically for your salon'
		}
	];
</script>

<div class="mt-8 grid gap-4">
	{#each features as feature, index}
		<FeatureCard
			iconName={feature.iconName}
			title={feature.title}
			description={feature.description}
		/>
	{/each}
</div>
