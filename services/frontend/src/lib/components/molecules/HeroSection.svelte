<script lang="ts">
	import Icon from '@iconify/svelte';
	export let title: string = 'Welcome to SalonIntel';
	export let subtitle: string = "Let's set up your AI marketing assistant";
	export let iconName: string = 'mdi:sparkles';
</script>

<div class="space-y-8 text-center">
	<!-- Hero Icon with Gradient Background -->
	<div
		class="mx-auto flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600"
	>
		<Icon icon={iconName} class="h-10 w-10 text-white" />
	</div>

	<!-- Hero Text Content -->
	<div class="space-y-4">
		<h1 class="text-2xl font-bold text-slate-800 md:text-3xl">
			{title}
		</h1>
		<p class="text-lg text-slate-600">
			{subtitle}
		</p>
	</div>
</div>
