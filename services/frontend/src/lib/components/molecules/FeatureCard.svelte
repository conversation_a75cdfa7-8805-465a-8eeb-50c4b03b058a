<script lang="ts">
	import { Card } from '$lib/components/ui/card';
	import Icon from '@iconify/svelte';

	export let iconName: string;
	export let title: string;
	export let description: string;
</script>

<Card class="flex items-center space-x-4 bg-slate-50 p-4">
	<div class="bg-slate flex h-10 w-10 items-center justify-center rounded-lg">
		<Icon icon={iconName} class="text-salon-blue h-5 w-5" />
	</div>
	<div class="text-left">
		<h3 class="font-medium text-slate-800">{title}</h3>
		<p class="text-sm text-slate-500">{description}</p>
	</div>
</Card>
