<script lang="ts">
	import Checkbox from '$lib/components/ui/checkbox/checkbox.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	import { RadioGroup, RadioGroupItem } from '$lib/components/ui/radio-group';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';

	import Icon from '@iconify/svelte';
	import type { OnboardingData } from '$lib/types/onboarding';

	// Props for consistency with other steps
	export let title: string;
	export let subtitle: string;
	export let icon: string;

	// Goals data binding
	export let goals: OnboardingData['goals'];

	const availableGoals = [
		{ id: 'increase-bookings', label: 'Increase online bookings', icon: 'mdi:calendar-plus' },
		{ id: 'grow-social', label: 'Grow social media following', icon: 'mdi:account-group' },
		{ id: 'attract-customers', label: 'Attract new customers', icon: 'mdi:account-plus' },
		{ id: 'improve-retention', label: 'Improve customer retention', icon: 'mdi:heart' },
		{ id: 'build-awareness', label: 'Build brand awareness', icon: 'mdi:bullhorn' },
		{ id: 'generate-reviews', label: 'Generate more reviews', icon: 'mdi:star' }
	];

	// Computed property to check if we should show the priority section
	$: showPrioritySection = goals.objectives.length >= 2;

	// Filter available goals to only show selected ones in priority section
	$: selectedGoalOptions = availableGoals.filter(goal => goals.objectives.includes(goal.id));

	function toggleGoal(goalId: string) {
		if (goals.objectives.includes(goalId)) {
			goals.objectives = goals.objectives.filter(g => g !== goalId);
			// Reset priority if the selected priority goal is removed
			if (goals.priority === goalId) {
				goals.priority = '';
			}
		} else {
			goals.objectives = [...goals.objectives, goalId];
		}
	}
</script>

<div class="space-y-8">
	<!-- Marketing Goals Selection -->
	<Card>
		<CardHeader>
			<CardTitle class="text-lg">Marketing Goals</CardTitle>
			<p class="text-sm text-gray-600">Select all goals that are important to your business</p>
		</CardHeader>
		<CardContent>
			<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
				{#each availableGoals as goal}
					<div class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
						<Checkbox
							id={goal.id}
							checked={goals.objectives.includes(goal.id)}
							onCheckedChange={() => toggleGoal(goal.id)}
						/>
						<Icon icon={goal.icon} class="h-5 w-5 text-gray-500" />
						<Label for={goal.id} class="text-sm font-medium text-gray-700 cursor-pointer flex-1">
							{goal.label}
						</Label>
					</div>
				{/each}
			</div>
		</CardContent>
	</Card>

	<!-- Priority Selection (only shown when 2+ goals are selected) -->
	{#if showPrioritySection}
		<Card>
			<CardHeader>
				<div class="flex items-center space-x-2">
					<Icon icon="mdi:star" class="h-5 w-5 text-yellow-500" />
					<CardTitle class="text-lg">What's Most Important?</CardTitle>
				</div>
				<p class="text-sm text-gray-600">
					Since you selected multiple goals, which one is your top priority right now?
				</p>
			</CardHeader>
			<CardContent>
				<RadioGroup bind:value={goals.priority} class="space-y-3">
					{#each selectedGoalOptions as goal}
						<div class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
							<RadioGroupItem value={goal.id} id={`priority-${goal.id}`} />
							<Icon icon={goal.icon} class="h-5 w-5 text-gray-500" />
							<Label for={`priority-${goal.id}`} class="text-sm font-medium cursor-pointer flex-1">
								{goal.label}
							</Label>
						</div>
					{/each}
				</RadioGroup>
			</CardContent>
		</Card>
	{/if}

	<!-- Info Section -->
	<div class="rounded-lg bg-green-50 p-4">
		<div class="flex items-start space-x-3">
			<Icon icon="mdi:lightbulb" class="h-5 w-5 text-green-500 mt-0.5" />
			<div class="text-sm">
				<p class="font-medium text-green-900">How this helps</p>
				<p class="text-green-700 mt-1">
					Understanding your goals helps us customize content suggestions, marketing strategies,
					and insights that align with what matters most to your business.
				</p>
			</div>
		</div>
	</div>
</div>