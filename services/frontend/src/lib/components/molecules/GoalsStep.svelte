<script lang="ts">
	import Checkbox from '$lib/components/ui/checkbox/checkbox.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	
	export let selectedGoals: string[] = [];
	
	const availableGoals = [
		'Increase online bookings',
		'Grow social media following',
		'Attract new customers',
		'Improve customer retention',
		'Build brand awareness',
		'Generate more reviews'
	];
	
	function toggleGoal(goal: string) {
		if (selectedGoals.includes(goal)) {
			selectedGoals = selectedGoals.filter(g => g !== goal);
		} else {
			selectedGoals = [...selectedGoals, goal];
		}
	}
</script>

<div class="space-y-6">
	<div class="text-center">
		<h2 class="text-2xl font-bold text-gray-900">What are your main goals?</h2>
		<p class="mt-2 text-gray-600">Select what you want to achieve with SalonIntel</p>
	</div>
	
	<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
		{#each availableGoals as goal}
			<div class="flex items-center space-x-3">
				<Checkbox
					id={goal}
					checked={selectedGoals.includes(goal)}
					onCheckedChange={() => toggleGoal(goal)}
				/>
				<Label for={goal} class="text-sm font-medium text-gray-700">
					{goal}
				</Label>
			</div>
		{/each}
	</div>
</div>