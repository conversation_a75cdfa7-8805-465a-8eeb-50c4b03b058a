<script lang="ts">
	import Input from '$lib/components/ui/input/input.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	
	export let instagram = '';
	export let facebook = '';
	export let tiktok = '';
	export let website = '';
</script>

<div class="space-y-6">
	<div class="text-center">
		<h2 class="text-2xl font-bold text-gray-900">Connect your social media</h2>
		<p class="mt-2 text-gray-600">Help us understand your current online presence</p>
	</div>
	
	<div class="space-y-4">
		<div>
			<Label for="instagram">Instagram Handle</Label>
			<Input
				id="instagram"
				bind:value={instagram}
				placeholder="@yourhandle"
				class="mt-1"
			/>
		</div>
		
		<div>
			<Label for="facebook">Facebook Page</Label>
			<Input
				id="facebook"
				bind:value={facebook}
				placeholder="Your Facebook page URL"
				class="mt-1"
			/>
		</div>
		
		<div>
			<Label for="tiktok">TikTok Handle</Label>
			<Input
				id="tiktok"
				bind:value={tiktok}
				placeholder="@yourhandle"
				class="mt-1"
			/>
		</div>
		
		<div>
			<Label for="website">Website</Label>
			<Input
				id="website"
				bind:value={website}
				placeholder="https://yourwebsite.com"
				class="mt-1"
			/>
		</div>
	</div>
</div>