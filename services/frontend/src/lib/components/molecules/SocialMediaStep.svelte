<script lang="ts">
	import Input from '$lib/components/ui/input/input.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	import Icon from '@iconify/svelte';
	import type { OnboardingData } from '$lib/types/onboarding';

	// Props for consistency with other steps
	export let title: string;
	export let subtitle: string;
	export let icon: string;

	// Social media data binding
	export let socialMedia: OnboardingData['socialMedia'];

	const socialPlatforms = [
		{
			key: 'instagram' as keyof typeof socialMedia,
			label: 'Instagram',
			icon: 'mdi:instagram',
			placeholder: '@yourhandle or https://instagram.com/yourhandle',
			color: 'text-pink-500'
		},
		{
			key: 'facebook' as keyof typeof socialMedia,
			label: 'Facebook',
			icon: 'mdi:facebook',
			placeholder: 'https://facebook.com/yourpage',
			color: 'text-blue-600'
		},
		{
			key: 'tiktok' as keyof typeof socialMedia,
			label: 'TikTok',
			icon: 'ic:baseline-tiktok',
			placeholder: '@yourhandle or https://tiktok.com/@yourhandle',
			color: 'text-black'
		},
		{
			key: 'linkedin' as keyof typeof socialMedia,
			label: 'LinkedIn',
			icon: 'mdi:linkedin',
			placeholder: 'https://linkedin.com/company/yourcompany',
			color: 'text-blue-700'
		}
	];
</script>

<div class="space-y-6">
	<div class="text-center">
		<p class="text-gray-600">
			Connect your social media accounts to help us understand your online presence. All fields are optional.
		</p>
	</div>

	<div class="space-y-6">
		{#each socialPlatforms as platform}
			<div class="space-y-2">
				<div class="flex items-center space-x-2">
					<Icon icon={platform.icon} class="h-5 w-5 {platform.color}" />
					<Label for={platform.key} class="text-sm font-medium">
						{platform.label}
					</Label>
				</div>
				<Input
					id={platform.key}
					bind:value={socialMedia[platform.key]}
					placeholder={platform.placeholder}
					class="w-full"
					type="text"
				/>
				<p class="text-xs text-gray-500">
					You can enter either a handle (e.g., @yourname) or a full URL
				</p>
			</div>
		{/each}
	</div>

	<div class="rounded-lg bg-blue-50 p-4">
		<div class="flex items-start space-x-3">
			<Icon icon="mdi:information" class="h-5 w-5 text-blue-500 mt-0.5" />
			<div class="text-sm">
				<p class="font-medium text-blue-900">Why do we need this?</p>
				<p class="text-blue-700 mt-1">
					Your social media presence helps us create more personalized content and understand your brand voice.
					This information is kept secure and will only be used to improve your experience.
				</p>
			</div>
		</div>
	</div>
</div>