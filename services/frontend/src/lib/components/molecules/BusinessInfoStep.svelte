<script lang="ts">
	import Input from '$lib/components/ui/input/input.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	import { RadioGroup, RadioGroupItem } from '$lib/components/ui/radio-group';
	import type { OnboardingData } from '$lib/types/onboarding';

	// Props for consistency with other steps
	export let title: string;
	export let subtitle: string;
	export let icon: string;

	// Business info data binding
	export let businessInfo: OnboardingData['businessInfo'];

	const companyTypes = [
		{ value: 'Beauty Salon', label: 'Beauty Salon' },
		{ value: 'Hair Salon', label: 'Hair Salon' },
		{ value: 'Nail Salon', label: 'Nail Salon' }
	] as const;
</script>

<div class="space-y-6">
	<!-- Business Name -->
	<div class="space-y-2">
		<Label for="salon-name">Salon Name *</Label>
		<Input
			id="salon-name"
			bind:value={businessInfo.salonName}
			placeholder="Enter your salon name"
			class="w-full"
		/>
	</div>

	<!-- Company Type -->
	<div class="space-y-3">
		<Label>Company Type *</Label>
		<RadioGroup bind:value={businessInfo.companyType} class="flex flex-col space-y-2">
			{#each companyTypes as type}
				<div class="flex items-center space-x-2">
					<RadioGroupItem value={type.value} id={type.value} />
					<Label for={type.value} class="text-sm font-normal">{type.label}</Label>
				</div>
			{/each}
		</RadioGroup>
	</div>

	<!-- Address Section -->
	<div class="space-y-4">
		<h3 class="text-lg font-medium text-gray-900">Address</h3>

		<div class="space-y-2">
			<Label for="street-address">Street Address *</Label>
			<Input
				id="street-address"
				bind:value={businessInfo.streetAddress}
				placeholder="123 Main Street"
				class="w-full"
			/>
		</div>

		<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
			<div class="space-y-2">
				<Label for="city">City *</Label>
				<Input
					id="city"
					bind:value={businessInfo.city}
					placeholder="City"
					class="w-full"
				/>
			</div>

			<div class="space-y-2">
				<Label for="postal-code">Postal Code *</Label>
				<Input
					id="postal-code"
					bind:value={businessInfo.postalCode}
					placeholder="12345"
					class="w-full"
				/>
			</div>
		</div>

		<div class="space-y-2">
			<Label for="country">Country *</Label>
			<Input
				id="country"
				bind:value={businessInfo.country}
				placeholder="Netherlands"
				class="w-full"
			/>
		</div>
	</div>

	<!-- Contact Information -->
	<div class="space-y-4">
		<h3 class="text-lg font-medium text-gray-900">Contact Information</h3>

		<div class="space-y-2">
			<Label for="phone">Phone Number *</Label>
			<Input
				id="phone"
				bind:value={businessInfo.phoneNumber}
				placeholder="+31 6 12345678"
				type="tel"
				class="w-full"
			/>
		</div>

		<div class="space-y-2">
			<Label for="email">Email Address *</Label>
			<Input
				id="email"
				bind:value={businessInfo.email}
				placeholder="<EMAIL>"
				type="email"
				class="w-full"
			/>
		</div>

		<div class="space-y-2">
			<Label for="website">Website (Optional)</Label>
			<Input
				id="website"
				bind:value={businessInfo.website}
				placeholder="https://www.yoursalon.com"
				type="url"
				class="w-full"
			/>
		</div>
	</div>
</div>