<script lang="ts">
	import Input from '$lib/components/ui/input/input.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	
	export let businessName = '';
	export let businessType = '';
</script>

<div class="space-y-6">
	<div class="text-center">
		<h2 class="text-2xl font-bold text-gray-900">Tell us about your business</h2>
		<p class="mt-2 text-gray-600">Help us personalize your experience</p>
	</div>
	
	<div class="space-y-4">
		<div>
			<Label for="business-name">Business Name</Label>
			<Input
				id="business-name"
				bind:value={businessName}
				placeholder="Enter your business name"
				class="mt-1"
			/>
		</div>
		
		<div>
			<Label for="business-type">Business Type</Label>
			<Input
				id="business-type"
				bind:value={businessType}
				placeholder="e.g., Hair Salon, Beauty Spa, Barbershop"
				class="mt-1"
			/>
		</div>
	</div>
</div>