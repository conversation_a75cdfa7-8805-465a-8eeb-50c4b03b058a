<script lang="ts">
	export let businessName = '';
	export let businessType = '';
	export let selectedServices: string[] = [];
	export let selectedGoals: string[] = [];
</script>

<div class="space-y-6">
	<div class="text-center">
		<h2 class="text-2xl font-bold text-gray-900">Almost done!</h2>
		<p class="mt-2 text-gray-600">Review your information before we create your profile</p>
	</div>
	
	<div class="space-y-4">
		<div class="rounded-lg border p-4">
			<h3 class="font-semibold text-gray-900">Business Information</h3>
			<p class="text-gray-600">Name: {businessName || 'Not provided'}</p>
			<p class="text-gray-600">Type: {businessType || 'Not provided'}</p>
		</div>
		
		<div class="rounded-lg border p-4">
			<h3 class="font-semibold text-gray-900">Services</h3>
			{#if selectedServices.length > 0}
				<ul class="list-disc list-inside text-gray-600">
					{#each selectedServices as service}
						<li>{service}</li>
					{/each}
				</ul>
			{:else}
				<p class="text-gray-600">No services selected</p>
			{/if}
		</div>
		
		<div class="rounded-lg border p-4">
			<h3 class="font-semibold text-gray-900">Goals</h3>
			{#if selectedGoals.length > 0}
				<ul class="list-disc list-inside text-gray-600">
					{#each selectedGoals as goal}
						<li>{goal}</li>
					{/each}
				</ul>
			{:else}
				<p class="text-gray-600">No goals selected</p>
			{/if}
		</div>
	</div>
</div>