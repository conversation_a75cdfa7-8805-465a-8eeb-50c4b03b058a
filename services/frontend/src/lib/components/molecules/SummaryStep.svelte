<script lang="ts">
	import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '$lib/components/ui/card';
	import Checkbox from '$lib/components/ui/checkbox/checkbox.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	import Icon from '@iconify/svelte';
	import type { OnboardingData } from '$lib/types/onboarding';

	// Props for consistency with other steps
	export let title: string;
	export let subtitle: string;
	export let icon: string;

	// Data from previous steps
	export let businessInfo: OnboardingData['businessInfo'];
	export let services: OnboardingData['services'];
	export let socialMedia: OnboardingData['socialMedia'];
	export let goals: OnboardingData['goals'];

	// Terms acceptance state
	let acceptTerms = false;
	let acceptPrivacy = false;
	let acceptMarketing = false;

	// Goal labels mapping
	const goalLabels: Record<string, string> = {
		'increase-bookings': 'Increase online bookings',
		'grow-social': 'Grow social media following',
		'attract-customers': 'Attract new customers',
		'improve-retention': 'Improve customer retention',
		'build-awareness': 'Build brand awareness',
		'generate-reviews': 'Generate more reviews'
	};

	// Get social media platforms that have values
	$: activeSocialMedia = Object.entries(socialMedia)
		.filter(([_, value]) => value && value.trim() !== '')
		.map(([platform, value]) => ({ platform, value }));
</script>

<div class="space-y-6">
	<!-- Summary Cards -->
	<div class="space-y-4">
		<!-- Business Information -->
		<Card>
			<CardHeader>
				<div class="flex items-center space-x-2">
					<Icon icon="mdi:store" class="h-5 w-5 text-blue-500" />
					<CardTitle class="text-lg">Business Information</CardTitle>
				</div>
			</CardHeader>
			<CardContent class="space-y-2">
				<div class="grid grid-cols-1 gap-2 text-sm">
					<div><span class="font-medium">Salon Name:</span> {businessInfo.salonName || 'Not provided'}</div>
					<div><span class="font-medium">Type:</span> {businessInfo.companyType}</div>
					<div><span class="font-medium">Address:</span>
						{#if businessInfo.streetAddress}
							{businessInfo.streetAddress}, {businessInfo.city} {businessInfo.postalCode}, {businessInfo.country}
						{:else}
							Not provided
						{/if}
					</div>
					<div><span class="font-medium">Phone:</span> {businessInfo.phoneNumber || 'Not provided'}</div>
					<div><span class="font-medium">Email:</span> {businessInfo.email || 'Not provided'}</div>
					{#if businessInfo.website}
						<div><span class="font-medium">Website:</span> {businessInfo.website}</div>
					{/if}
				</div>
			</CardContent>
		</Card>

		<!-- Services -->
		<Card>
			<CardHeader>
				<div class="flex items-center space-x-2">
					<Icon icon="mdi:scissors-cutting" class="h-5 w-5 text-purple-500" />
					<CardTitle class="text-lg">Services & Positioning</CardTitle>
				</div>
			</CardHeader>
			<CardContent class="space-y-3">
				<div>
					<span class="font-medium text-sm">Services:</span>
					{#if services.categories.length > 0}
						<div class="mt-1 flex flex-wrap gap-2">
							{#each services.categories as service}
								<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
									{service}
								</span>
							{/each}
						</div>
					{:else}
						<span class="text-gray-500 text-sm">No services selected</span>
					{/if}
				</div>
				<div>
					<span class="font-medium text-sm">Pricing Tier:</span>
					<span class="ml-2 text-sm capitalize">{services.pricingTier}</span>
				</div>
				{#if services.mission}
					<div>
						<span class="font-medium text-sm">Mission:</span>
						<p class="mt-1 text-sm text-gray-600">{services.mission}</p>
					</div>
				{/if}
			</CardContent>
		</Card>

		<!-- Social Media -->
		{#if activeSocialMedia.length > 0}
			<Card>
				<CardHeader>
					<div class="flex items-center space-x-2">
						<Icon icon="mdi:share-variant" class="h-5 w-5 text-green-500" />
						<CardTitle class="text-lg">Social Media</CardTitle>
					</div>
				</CardHeader>
				<CardContent>
					<div class="space-y-2">
						{#each activeSocialMedia as { platform, value }}
							<div class="flex items-center space-x-2 text-sm">
								<Icon icon={platform === 'instagram' ? 'mdi:instagram' :
											platform === 'facebook' ? 'mdi:facebook' :
											platform === 'tiktok' ? 'ic:baseline-tiktok' :
											'mdi:linkedin'}
									  class="h-4 w-4" />
								<span class="font-medium capitalize">{platform}:</span>
								<span class="text-gray-600">{value}</span>
							</div>
						{/each}
					</div>
				</CardContent>
			</Card>
		{/if}

		<!-- Goals -->
		<Card>
			<CardHeader>
				<div class="flex items-center space-x-2">
					<Icon icon="mdi:target" class="h-5 w-5 text-orange-500" />
					<CardTitle class="text-lg">Marketing Goals</CardTitle>
				</div>
			</CardHeader>
			<CardContent class="space-y-3">
				{#if goals.objectives.length > 0}
					<div>
						<span class="font-medium text-sm">Selected Goals:</span>
						<div class="mt-1 space-y-1">
							{#each goals.objectives as goalId}
								<div class="flex items-center space-x-2">
									<Icon icon="mdi:check-circle" class="h-4 w-4 text-green-500" />
									<span class="text-sm">{goalLabels[goalId] || goalId}</span>
									{#if goals.priority === goalId}
										<Icon icon="mdi:star" class="h-4 w-4 text-yellow-500" />
									{/if}
								</div>
							{/each}
						</div>
					</div>
					{#if goals.priority}
						<div>
							<span class="font-medium text-sm">Top Priority:</span>
							<span class="ml-2 text-sm">{goalLabels[goals.priority] || goals.priority}</span>
						</div>
					{/if}
				{:else}
					<span class="text-gray-500 text-sm">No goals selected</span>
				{/if}
			</CardContent>
		</Card>
	</div>

	<!-- Terms and Conditions -->
	<Card>
		<CardHeader>
			<CardTitle class="text-lg">Terms & Conditions</CardTitle>
			<p class="text-sm text-gray-600">Please review and accept our terms to continue</p>
		</CardHeader>
		<CardContent class="space-y-4">
			<div class="flex items-start space-x-3">
				<Checkbox id="terms" bind:checked={acceptTerms} />
				<Label for="terms" class="text-sm leading-relaxed">
					I agree to the <a href="/terms" class="text-blue-600 hover:underline" target="_blank">Terms of Service</a>
					and understand that SalonIntel will help manage my salon's marketing activities.
				</Label>
			</div>

			<div class="flex items-start space-x-3">
				<Checkbox id="privacy" bind:checked={acceptPrivacy} />
				<Label for="privacy" class="text-sm leading-relaxed">
					I agree to the <a href="/privacy" class="text-blue-600 hover:underline" target="_blank">Privacy Policy</a>
					and consent to the processing of my business data to provide personalized marketing insights.
				</Label>
			</div>

			<div class="flex items-start space-x-3">
				<Checkbox id="marketing" bind:checked={acceptMarketing} />
				<Label for="marketing" class="text-sm leading-relaxed">
					I would like to receive marketing tips, product updates, and promotional emails from SalonIntel.
					<span class="text-gray-500">(Optional)</span>
				</Label>
			</div>
		</CardContent>
	</Card>

	<!-- Ready to go message -->
	{#if acceptTerms && acceptPrivacy}
		<div class="rounded-lg bg-green-50 p-4">
			<div class="flex items-center space-x-3">
				<Icon icon="mdi:check-circle" class="h-6 w-6 text-green-500" />
				<div>
					<p class="font-medium text-green-900">You're all set!</p>
					<p class="text-green-700 text-sm">Click "Complete Setup" to create your SalonIntel profile and start your marketing journey.</p>
				</div>
			</div>
		</div>
	{/if}
</div>