<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { locale } from 'svelte-intl-precompile';
	import { browser } from '$app/environment';
	
	// Import Icon component correctly
	import Icon from '@iconify/svelte';

	const languageOptions = [
		{ code: 'en', label: 'English', icon: 'flag:gb-4x3' },
		{ code: 'nl', label: 'Nederlands', icon: 'flag:nl-4x3' }
	];

	function handleChange(lang: string) {
		$locale = lang;
		if (browser) localStorage.setItem('lang', lang);
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger class="px-2 py-1 flex items-center gap-2">
		{#if browser}
			<Icon icon={languageOptions.find(l => l.code === $locale)?.icon} class="w-5 h-5" />
		{/if}
		<span>{languageOptions.find(l => l.code === $locale)?.label}</span>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content>
		<DropdownMenu.Group>
			{#each languageOptions as lang}
				<DropdownMenu.Item on:click={() => handleChange(lang.code)}>
					<div class="flex items-center gap-2">
						{#if browser}
							<Icon icon={lang.icon} class="w-5 h-5" />
						{/if}
						<span>{lang.label}</span>
					</div>
				</DropdownMenu.Item>
			{/each}
		</DropdownMenu.Group>
	</DropdownMenu.Content>
</DropdownMenu.Root>
