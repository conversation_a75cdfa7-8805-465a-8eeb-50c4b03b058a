<script lang="ts">
	import Progress from '$lib/components/ui/progress/progress.svelte';
	import StepNavigationButtons from '$lib/components/molecules/StepNavigationButtons.svelte';
	import HeroSection from '$lib/components/molecules/HeroSection.svelte';
	import type { OnboardingStep } from '$lib/types/onboarding';

	export let currentStep: OnboardingStep;
	export let totalSteps: OnboardingStep = 6;
	export let progressLabel: string;
	export let title: string;
	export let subtitle: string = '';
	export let icon: string = 'mdi:sparkles';
	export let nextLabel: string = 'Continue';
	export let nextDisabled: boolean = false;
	export let isSubmitting: boolean = false;

	// Event dispatchers for parent communication
	import { createEventDispatcher } from 'svelte';
	const dispatch = createEventDispatcher();

	function handleBack() {
		dispatch('back');
	}

	function handleNext() {
		dispatch('next');
	}
</script>

<div class="from-salon-teal/10 to-salon-purple/10 min-h-screen bg-gradient-to-br via-white">
	<div class="container mx-auto max-w-2xl px-4 py-8">
		<Progress
			label={progressLabel}
			showSteps={true}
			{currentStep}
			{totalSteps}
			value={currentStep}
			max={totalSteps}
		/>

		<div class="animate-fade-in mt-8 rounded-2xl bg-white p-6 shadow-xl md:p-8">
			<!-- Hero Section -->
			<div class="mb-8">
				<HeroSection {title} {subtitle} iconName={icon} />
			</div>

			<div class="space-y-6">
				<slot />
			</div>

			<StepNavigationButtons
				{currentStep}
				{totalSteps}
				{nextLabel}
				{nextDisabled}
				{isSubmitting}
				on:back={handleBack}
				on:next={handleNext}
			/>
		</div>
	</div>
</div>

<style>
	.animate-fade-in {
		animation: fadeIn 0.5s ease-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
