<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import StepLayout from './StepLayout.svelte';
	import WelcomeStep from '../molecules/WelcomeStep.svelte';
	import BusinessInfoStep from '../molecules/BusinessInfoStep.svelte';
	import ServicesStep from '../molecules/ServicesStep.svelte';
	import SocialMediaStep from '../molecules/SocialMediaStep.svelte';
	import GoalsStep from '../molecules/GoalsStep.svelte';
	import SummaryStep from '../molecules/SummaryStep.svelte';
	import type { OnboardingStep, OnboardingData } from '$lib/types/onboarding';

	const dispatch = createEventDispatcher();

	// State management using Svelte runes
	let currentStep = $state<OnboardingStep>(1);
	const totalSteps: OnboardingStep = 6;
	let isSubmitting = $state(false);

	// Form data using runes
	let businessInfo = $state({
		salonName: '',
		streetAddress: '',
		city: '',
		postalCode: '',
		country: '',
		phoneNumber: '',
		email: '',
		website: '',
		logo: '',
		companyType: 'Beauty Salon' as const
	});

	let services = $state({
		categories: [] as string[],
		pricingTier: 'mid-range' as const,
		mission: ''
	});

	let socialMedia = $state({
		instagram: '',
		facebook: '',
		tiktok: '',
		linkedin: ''
	});

	let goals = $state({
		objectives: [] as string[],
		priority: ''
	});

	// Step configuration with icons
	const stepConfig = {
		1: {
			title: 'Welcome to SalonIntel',
			subtitle: "Let's set up your AI marketing assistant",
			icon: 'mdi:sparkles',
			nextLabel: 'Start'
		},
		2: {
			title: 'Business Information',
			subtitle: 'Tell us about your business',
			icon: 'mdi:store',
			nextLabel: 'Continue'
		},
		3: {
			title: 'Your Services',
			subtitle: 'What do you offer?',
			icon: 'mdi:scissors-cutting',
			nextLabel: 'Continue'
		},
		4: {
			title: 'Social Media',
			subtitle: 'Connect your online presence',
			icon: 'mdi:share-variant',
			nextLabel: 'Continue'
		},
		5: {
			title: 'Your Goals',
			subtitle: 'What do you want to achieve?',
			icon: 'mdi:target',
			nextLabel: 'Continue'
		},
		6: {
			title: 'Summary',
			subtitle: 'Review and finish setup',
			icon: 'mdi:check-circle',
			nextLabel: 'Complete Setup'
		}
	};

	// Computed properties using runes
	let currentConfig = $derived(stepConfig[currentStep]);
	let nextDisabled = $derived(getNextDisabled(currentStep));

	function getNextDisabled(step: OnboardingStep): boolean {
		// Disable all validation for testing - you can re-enable later
		return false;
	}

	function handleNext() {
		if (currentStep < totalSteps) {
			currentStep = (currentStep + 1) as OnboardingStep;
		} else {
			// Complete the onboarding
			handleComplete();
		}
	}

	function handleBack() {
		if (currentStep > 1) {
			currentStep = (currentStep - 1) as OnboardingStep;
		}
	}

	async function handleComplete() {
		isSubmitting = true;

		const onboardingData: OnboardingData = {
			businessInfo,
			services,
			socialMedia,
			goals
		};

		try {
			// Dispatch completion event with data
			dispatch('complete', onboardingData);
		} finally {
			isSubmitting = false;
		}
	}
</script>

<StepLayout
	{currentStep}
	{totalSteps}
	title={currentConfig.title}
	subtitle={currentConfig.subtitle}
	icon={currentConfig.icon}
	nextLabel={currentConfig.nextLabel}
	{nextDisabled}
	{isSubmitting}
	progressLabel="Setting up your profile"
	on:next={handleNext}
	on:back={handleBack}
>
	{#if currentStep === 1}
		<WelcomeStep
			title={currentConfig.title}
			subtitle={currentConfig.subtitle}
			icon={currentConfig.icon}
		/>
	{:else if currentStep === 2}
		<BusinessInfoStep
			bind:businessInfo
			title={currentConfig.title}
			subtitle={currentConfig.subtitle}
			icon={currentConfig.icon}
		/>
	{:else if currentStep === 3}
		<ServicesStep
			bind:services
			title={currentConfig.title}
			subtitle={currentConfig.subtitle}
			icon={currentConfig.icon}
		/>
	{:else if currentStep === 4}
		<SocialMediaStep
			bind:socialMedia
			title={currentConfig.title}
			subtitle={currentConfig.subtitle}
			icon={currentConfig.icon}
		/>
	{:else if currentStep === 5}
		<GoalsStep
			bind:goals
			title={currentConfig.title}
			subtitle={currentConfig.subtitle}
			icon={currentConfig.icon}
		/>
	{:else if currentStep === 6}
		<SummaryStep
			{businessInfo}
			{services}
			{socialMedia}
			{goals}
			title={currentConfig.title}
			subtitle={currentConfig.subtitle}
			icon={currentConfig.icon}
		/>
	{/if}
</StepLayout>
