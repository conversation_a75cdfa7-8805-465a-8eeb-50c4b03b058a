<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import StepLayout from './StepLayout.svelte';
	import WelcomeStep from '../molecules/WelcomeStep.svelte';
	import BusinessInfoStep from '../molecules/BusinessInfoStep.svelte';
	import ServicesStep from '../molecules/ServicesStep.svelte';
	import SocialMediaStep from '../molecules/SocialMediaStep.svelte';
	import GoalsStep from '../molecules/GoalsStep.svelte';
	import SummaryStep from '../molecules/SummaryStep.svelte';
	import type { OnboardingStep } from '$lib/types/onboarding';

	const dispatch = createEventDispatcher();

	// State management
	let currentStep: OnboardingStep = 1;
	const totalSteps: OnboardingStep = 6;
	let isSubmitting = false;

	// Form data
	let businessName = '';
	let businessType = '';
	let selectedServices: string[] = [];
	let instagram = '';
	let facebook = '';
	let tiktok = '';
	let website = '';
	let selectedGoals: string[] = [];

	// Step configuration
	const stepConfig = {
		1: {
			title: 'Welcome to SalonIntel',
			subtitle: "Let's set up your AI marketing assistant",
			nextLabel: 'Start'
		},
		2: {
			title: 'Business Information',
			subtitle: 'Tell us about your business',
			nextLabel: 'Continue'
		},
		3: { title: 'Your Services', subtitle: 'What do you offer?', nextLabel: 'Continue' },
		4: { title: 'Social Media', subtitle: 'Connect your online presence', nextLabel: 'Continue' },
		5: { title: 'Your Goals', subtitle: 'What do you want to achieve?', nextLabel: 'Continue' },
		6: { title: 'Summary', subtitle: 'Review and finish setup', nextLabel: 'Complete Setup' }
	};

	// Computed properties
	$: currentConfig = stepConfig[currentStep];
	$: nextDisabled = getNextDisabled(currentStep);

	function getNextDisabled(step: OnboardingStep): boolean {
		// Disable all validation for testing - you can re-enable later
		return false;
	}

	function handleNext() {
		if (currentStep < totalSteps) {
			currentStep = (currentStep + 1) as OnboardingStep;
		} else {
			// Complete the onboarding
			handleComplete();
		}
	}

	function handleBack() {
		if (currentStep > 1) {
			currentStep = (currentStep - 1) as OnboardingStep;
		}
	}

	async function handleComplete() {
		isSubmitting = true;

		const onboardingData = {
			businessName,
			businessType,
			selectedServices,
			socialMedia: { instagram, facebook, tiktok, website },
			selectedGoals
		};

		try {
			// Dispatch completion event with data
			dispatch('complete', onboardingData);
		} finally {
			isSubmitting = false;
		}
	}
</script>

<StepLayout
	{currentStep}
	{totalSteps}
	title={currentConfig.title}
	subtitle={currentConfig.subtitle}
	nextLabel={currentConfig.nextLabel}
	{nextDisabled}
	{isSubmitting}
	progressLabel="Setting up your profile"
	on:next={handleNext}
	on:back={handleBack}
>
	{#if currentStep === 1}
		<WelcomeStep />
	{:else if currentStep === 2}
		<BusinessInfoStep bind:businessName bind:businessType />
	{:else if currentStep === 3}
		<ServicesStep bind:selectedServices />
	{:else if currentStep === 4}
		<SocialMediaStep bind:instagram bind:facebook bind:tiktok bind:website />
	{:else if currentStep === 5}
		<GoalsStep bind:selectedGoals />
	{:else if currentStep === 6}
		<SummaryStep {businessName} {businessType} {selectedServices} {selectedGoals} />
	{/if}
</StepLayout>
