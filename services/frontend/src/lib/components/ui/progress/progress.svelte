<script lang="ts">
	import { Progress as ProgressPrimitive } from "bits-ui";
	import { cn } from "$lib/utils.js";

	type $$Props = ProgressPrimitive.Props & {
		label?: string;
		valueLabel?: string;
	};

	let className: $$Props["class"] = undefined;
	export let max: $$Props["max"] = 100;
	export let value: $$Props["value"] = undefined;
	export let label: string | undefined = undefined;
	export let valueLabel: string | undefined = undefined;
	export { className as class };

	// Generate a simple unique ID for accessibility
	const labelId = `progress-label-${Math.random().toString(36).slice(2, 11)}`;
</script>

{#if label || valueLabel}
	<div class="flex items-center justify-between text-sm font-medium mb-2">
		{#if label}
			<span id={labelId}>{label}</span>
		{/if}
		{#if valueLabel}
			<span>{valueLabel}</span>
		{/if}
	</div>
{/if}

<ProgressPrimitive.Root
	class={cn("bg-secondary relative h-4 w-full overflow-hidden rounded-full", className)}
	aria-labelledby={label ? labelId : undefined}
	aria-valuetext={valueLabel}
	{max}
	{value}
	{...$$restProps}
>
	<div
		class="bg-primary h-full w-full flex-1 transition-all"
		style={`transform: translateX(-${100 - (100 * (value ?? 0)) / (max ?? 1)}%)`}
	></div>
</ProgressPrimitive.Root>
