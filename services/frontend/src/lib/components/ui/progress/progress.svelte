<script lang="ts">
	import { Progress as ProgressPrimitive } from "bits-ui";
	import { cn } from "$lib/utils.js";

	import Icon from "@iconify/svelte";

	type $$Props = ProgressPrimitive.Props & {
		label?: string;
		valueLabel?: string;
		showSteps?: boolean;
		currentStep?: number;
		totalSteps?: number;
	};

	let className: $$Props["class"] = undefined;
	export let max: $$Props["max"] = 100;
	export let value: $$Props["value"] = undefined;
	export let label: string | undefined = undefined;
	export let valueLabel: string | undefined = undefined;
	export let showSteps: boolean = false;
	export let currentStep: number = 1;
	export let totalSteps: number = 6;
	export { className as class };

	// Generate a simple unique ID for accessibility
	const labelId = `progress-label-${Math.random().toString(36).slice(2, 11)}`;

	// Calculate step positions for visual indicators
	$: stepPositions = Array.from({ length: totalSteps }, (_, i) => ({
		step: i + 1,
		position: i === 0 ? 0 : i === totalSteps - 1 ? 100 : (i / (totalSteps - 1)) * 100,
		isCompleted: i + 1 < currentStep,
		isCurrent: i + 1 === currentStep
	}));

	// Calculate progress percentage
	$: progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;
</script>

{#if showSteps}
	<!-- Enhanced Progress Bar with Step Indicators (like in screenshot) -->
	<div class="mb-8">
		<!-- Progress Label and Percentage -->
		<div class="flex items-center justify-between mb-4">
			{#if label}
				<span class="text-lg font-medium text-gray-900">{label}</span>
			{/if}
			<span class="text-lg font-medium text-blue-600">{Math.round(progressPercentage)}% Complete</span>
		</div>

		<!-- Progress Bar with Steps -->
		<div class="relative">
			<!-- Background Progress Bar -->
			<div class="h-2 bg-gray-200 rounded-full">
				<!-- Filled Progress -->
				<div
					class="h-2 bg-gradient-to-r from-green-400 to-blue-500 rounded-full transition-all duration-500 ease-out"
					style={`width: ${progressPercentage}%`}
				></div>
			</div>

			<!-- Step Indicators -->
			<div class="absolute -top-4 w-full">
				{#each stepPositions as { step, position, isCompleted, isCurrent }}
					<div
						class="absolute transform -translate-x-1/2"
						style={`left: ${position}%`}
					>
						{#if isCompleted}
							<!-- Completed Step - Green with Checkmark -->
							<div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center border-2 border-white shadow-md">
								<Icon icon="mdi:check" class="h-4 w-4 text-white" />
							</div>
						{:else if isCurrent}
							<!-- Current Step - Blue with Number -->
							<div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white shadow-md">
								<span class="text-white text-sm font-medium">{step}</span>
							</div>
						{:else}
							<!-- Future Step - Gray with Number -->
							<div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center border-2 border-white shadow-md">
								<span class="text-gray-600 text-sm font-medium">{step}</span>
							</div>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	</div>
{:else}
	<!-- Standard Progress Bar -->
	{#if label || valueLabel}
		<div class="flex items-center justify-between text-sm font-medium mb-2">
			{#if label}
				<span id={labelId}>{label}</span>
			{/if}
			{#if valueLabel}
				<span>{valueLabel}</span>
			{/if}
		</div>
	{/if}

	<ProgressPrimitive.Root
		class={cn("bg-secondary relative h-4 w-full overflow-hidden rounded-full", className)}
		aria-labelledby={label ? labelId : undefined}
		aria-valuetext={valueLabel}
		{max}
		{value}
		{...$$restProps}
	>
		<div
			class="bg-primary h-full w-full flex-1 transition-all"
			style={`transform: translateX(-${100 - (100 * (value ?? 0)) / (max ?? 1)}%)`}
		></div>
	</ProgressPrimitive.Root>
{/if}
