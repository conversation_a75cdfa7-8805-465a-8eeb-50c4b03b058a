import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public';
import { createBrowserClient } from '@supabase/ssr';
import { invalidate } from '$app/navigation';

export const supabase = createBrowserClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY);

supabase.auth.onAuthStateChange((event, session) => {
  if (session?.expires_at) {
    const expiresInMs = session.expires_at * 1000 - Date.now();
    const refreshTimeout = setTimeout(() => {
      invalidate('supabase:auth');
    }, expiresInMs);
    // No return value needed for onAuthStateChange callback
  }
});