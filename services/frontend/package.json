{"name": "frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "dev:root-env": "node -r dotenv/config ../../node_modules/vite/bin/vite.js dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@iconify/svelte": "^5.0.0", "@lucide/svelte": "^0.511.0", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-node": "^2.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "bits-ui": "^0.22.0", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "formsnap": "^1.0.1", "globals": "^16.0.0", "lucide-svelte": "^0.511.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.10", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "sveltekit-superforms": "^2.25.0", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tw-animate-css": "^1.3.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "zod": "^3.25.48"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "dotenv": "^16.5.0", "svelte-intl-precompile": "^0.12.3", "tailwindcss": "^3.4.17"}}