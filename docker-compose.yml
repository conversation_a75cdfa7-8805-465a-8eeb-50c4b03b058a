services:
  frontend:
    build:
      context: ./services/frontend
      dockerfile: Dockerfile
      target: development
    ports:
      - "5173:5173"
    volumes:
      - ./services/frontend:/app
    env_file:
      - ./.env
    environment:
      - NODE_ENV=development
      - DOCKER_ENVIRONMENT=true
      - DOCKER_SUPABASE_URL=http://host.docker.internal:54321
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - ai-service
      - api-gateway

  ai-service:
    build:
      context: ./services/ai-service
      dockerfile: Dockerfile
      target: development
    ports:
      - "8001:8001"
    volumes:
      - ./services/ai-service:/app
    env_file:
      - ./.env
    environment:
      - PYTHON_ENV=development
    extra_hosts:
      - "host.docker.internal:host-gateway"

  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
      target: development
    ports:
      - "8080:8080"
    volumes:
      - ./services/api-gateway:/app
    env_file:
      - ./.env
    environment:
      - GO_ENV=development
    extra_hosts:
      - "host.docker.internal:host-gateway"
