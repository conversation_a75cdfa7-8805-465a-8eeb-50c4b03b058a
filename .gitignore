# Git
.DS_Store
.env
.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local
.idea/
.vscode/

# Node.js
node_modules
dist
build
.svelte-kit
yarn.lock
pnpm-lock.yaml

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
pip-wheel-metadata/
.tox/
.coverage
.pytest_cache/
htmlcov/
.mypy_cache/
.ruff_cache/

# Golang
bin/
pkg/
*.exe
*.dll
*.so
*.dylib
*.test
*.out

# Docker
.dockerignore
docker-compose.override.yml

# Supabase
supabase/.temp/
supabase/db/