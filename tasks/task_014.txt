# Task ID: 14
# Title: Implement Website Crawling and Data Ingestion
# Status: pending
# Dependencies: 4
# Priority: medium
# Description: Develop the functionality to asynchronously crawl user websites using crawl4ai, retrieve pricelist, social media handles, and blog posts, store raw markdown and vector embeddings in Supabase, and display summary counts and status indicators in the frontend app.
# Details:


# Test Strategy:


# Subtasks:
## 1. Implement website crawling and data ingestion [pending]
### Dependencies: None
### Description: Integrate crawl4ai to crawl the user's website, retrieve data, and store it in Supabase.
### Details:


## 2. Implement "in-progress" status indicator [pending]
### Dependencies: 14.1
### Description: Display an "in-progress" status indicator in the frontend app while crawling and data ingestion are ongoing.
### Details:


## 3. Implement data summarization and pricelist display [pending]
### Dependencies: 14.1, 14.2
### Description: Show summary counts in the user app and display the pricelist on a separate page.
### Details:


