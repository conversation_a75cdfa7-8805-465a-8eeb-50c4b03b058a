# Task ID: 1
# Title: Setup Project Foundation
# Status: done
# Dependencies: None
# Priority: high
# Description: Initialize the project repository with Docker environments, CI/CD pipelines, and basic project structure for SvelteKit frontend, Python components, and Golang utilities.
# Details:
1. Create Git repository
2. Setup Docker environments for development:
   - Frontend container: Multi-stage Dockerfile with Node.js lts-bookworm for builder/dependencies and distroless/nodejs22-debian12 for production
   - AI service container: Python 3.12 with multi-stage Dockerfile using 3.12-bookworm and distroless/python3-debian12 for production
   - API Gateway container: Golang 1.24-bookworm with multi-stage Dockerfile
   - Supabase local development setup
3. Configure Railway.app for direct deployment from GitHub repository
4. Initialize SvelteKit 2.x project with Tailwind CSS, ShadcnUI, ESLint, and Prettier (manual setup)
5. Create basic Python service structure for AI components
6. Setup Golang service structure for API Gateway
7. Configure Railway.app deployment scripts
8. Implement basic HTTPS and security configurations

# Test Strategy:
1. Verify Docker containers build and run successfully
2. Ensure Railway.app deployment triggers correctly from GitHub
3. Confirm development environment can be spun up with a single command
4. Test basic connectivity between services
5. Validate Railway.app deployment process with a minimal test deployment
6. Verify Supabase local development is working correctly
7. Test SvelteKit frontend with ShadcnUI components
8. Verify Tailwind CSS is properly configured

# Subtasks:
## 1. Initialize Git Repository and Project Structure [done]
### Dependencies: None
### Description: Create the base repository with proper directory structure, README, .gitignore, and initial documentation.
### Details:
Set up a new Git repository with main branches (main, development). Create a comprehensive README.md with project overview, setup instructions, and contribution guidelines. Configure .gitignore for Python, Golang, and Node.js. Establish directory structure for microservices architecture with separate folders for each service.

## 3. Configure Docker Environment for Node.js (SvelteKit) [done]
### Dependencies: 1.1
### Description: Set up Docker configuration for SvelteKit frontend with Tailwind CSS and ShadcnUI.
### Details:
Create a multi-stage Dockerfile for the frontend service:
- Builder and dependencies layers: Use Node.js lts-bookworm image
- Development layer: Use node:lts-bookworm or node:lts-bookworm-slim with development tools and hot-reloading
- Production layer: Use gcr.io/distroless/nodejs22-debian12 for a secure and small container

Follow the example structure:
```dockerfile
# Stage 1: Build everything
FROM node:lts-bookworm AS builder
WORKDIR /app
COPY package.json package-lock.json ./ 
RUN npm ci
COPY . . 
RUN npm run build

# Stage 2: Install production dependencies
FROM node:lts-bookworm AS deps
WORKDIR /app
COPY package.json package-lock.json ./ 
RUN npm ci --omit=dev

# Stage 3: Development layer with hot-reloading
FROM node:lts-bookworm AS development
WORKDIR /app
COPY package.json package-lock.json ./ 
RUN npm ci
COPY . . 
CMD ["npm", "run", "dev"]

# Stage 4: Create the final distroless image
FROM gcr.io/distroless/nodejs22-debian12
WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=deps /app/node_modules ./node_modules
COPY package.json ./ 
CMD ["dist/server.js"]
```

Ensure proper hot-reloading functionality for development.

## 4. Configure Docker Environment for Python [done]
### Dependencies: 1.1
### Description: Set up Docker configuration for Python AI service with necessary libraries and dependencies.
### Details:
Create a multi-stage Dockerfile for the ai-service:
- Builder and dependencies layers: Use Python 3.12-bookworm image
- Development layer: Use Python 3.12-bookworm with development tools and hot-reloading
- Production layer: Use distroless/python3-debian12 image matching the 3.12 version

Follow the example structure:
```dockerfile
# Stage 1: Builder
FROM python:3.12-bookworm AS builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .

# Stage 2: Development
FROM python:3.12-bookworm AS development
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
CMD ["python", "app.py"]

# Stage 3: Production
FROM distroless/python3-debian12
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /app .
CMD ["app.py"]
```

Ensure proper logging, monitoring configurations, and compatibility with async processing.

## 5. Configure Docker Environment for Golang [done]
### Dependencies: 1.1
### Description: Establish Docker configuration for Golang API Gateway service.
### Details:
Create a multi-stage Dockerfile for the api-gateway service:
- Builder and dependencies layers: Use Golang 1.24-bookworm image
- Development layer: Use Golang 1.24-bookworm with development tools
- Production layer: Use minimal Golang 1.24-bookworm or distroless image

Configure Go modules, dependency management, appropriate compiler flags, and build optimizations. Ensure proper error handling and logging configurations.

## 6. Set up Supabase Local Development [done]
### Dependencies: 1.1
### Description: Configure Supabase local development environment with vector extension enabled.
### Details:
Install Supabase CLI for local development. Initialize local Supabase project. Enable vector extension through Supabase CLI or dashboard. Configure database initialization scripts. Set up proper volume mapping for data persistence. Configure database security settings, users, and permissions. Implement backup and recovery procedures.

## 7. Create Docker Compose Configuration [done]
### Dependencies: 1.3, 1.4, 1.5, 1.6
### Description: Develop a comprehensive docker-compose.yml file to orchestrate all services and their interactions.
### Details:
Create docker-compose.yml with service definitions for all containers. Configure networking between services. Set up volume mappings for persistent data. Define environment variables and secrets management. Configure health checks and restart policies. Ensure Supabase services are properly integrated.

## 8. Initialize SvelteKit Project with Tailwind and ShadcnUI [done]
### Dependencies: 1.3
### Description: Set up the SvelteKit frontend project with necessary configurations and dependencies.
### Details:
Create a new SvelteKit 2.x project using npm create svelte@latest. Install and configure Tailwind CSS with proper configuration. Set up ShadcnUI component library and configure theme. Create basic layout and component structure. Set up routing and basic page templates. Configure environment variables for API connections.

## 9. Configure Railway.app Deployment Setup [done]
### Dependencies: 1.7, 1.8
### Description: Set up Railway.app configuration for continuous deployment of the application.
### Details:
Create Railway.app project and configure service definitions. Set up environment variables and secrets. Configure database provisioning. Set up custom domains and SSL certificates. Implement deployment hooks and notifications. Configure scaling parameters and resource allocation.

## 10. Implement Security Configurations and CI/CD Pipeline [done]
### Dependencies: 1.9
### Description: Set up security measures and continuous integration/deployment pipeline for the project.
### Details:
Configure GitHub Actions or similar CI/CD tool. Implement security scanning for dependencies and Docker images. Set up automated testing in the pipeline. Configure deployment approvals and rollback procedures. Implement secrets management. Set up monitoring and alerting for the deployed application.

## 11. Reorganize services into a 'services' folder [done]
### Dependencies: None
### Description: Move backend/golang, backend/python, and frontend/sveltekit into a new 'services' directory to declutter the root folder.
### Details:


## 12. Create multi-stage Dockerfile for Python service [done]
### Dependencies: None
### Description: Implement a multi-stage Dockerfile for the Python backend, including a builder stage, a development stage with hot-reloading, and a minimal production stage.
### Details:
Implement the multi-stage Dockerfile for the ai-service following the provided example:
```dockerfile
# Stage 1: Builder
FROM python:3.12-bookworm AS builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .

# Stage 2: Development
FROM python:3.12-bookworm AS development
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
CMD ["python", "app.py"]

# Stage 3: Production
FROM distroless/python3-debian12
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /app .
CMD ["app.py"]
```

Ensure the Dockerfile is placed in the services/ai-service directory and properly configured for secure and small production images.

## 13. Create multi-stage Dockerfile for SvelteKit service [done]
### Dependencies: None
### Description: Implement a multi-stage Dockerfile for the SvelteKit frontend, including a build stage, a development stage with hot-reloading, and a minimal production stage.
### Details:
Implement the multi-stage Dockerfile for the frontend service following the provided example:
```dockerfile
# Stage 1: Build everything
FROM node:lts-bookworm AS builder
WORKDIR /app
COPY package.json package-lock.json ./ 
RUN npm ci
COPY . . 
RUN npm run build

# Stage 2: Install production dependencies
FROM node:lts-bookworm AS deps
WORKDIR /app
COPY package.json package-lock.json ./ 
RUN npm ci --omit=dev

# Stage 3: Development layer with hot-reloading
FROM node:lts-bookworm AS development
WORKDIR /app
COPY package.json package-lock.json ./ 
RUN npm ci
COPY . . 
CMD ["npm", "run", "dev"]

# Stage 4: Create the final distroless image
FROM gcr.io/distroless/nodejs22-debian12
WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=deps /app/node_modules ./node_modules
COPY package.json ./ 
CMD ["dist/server.js"]
```

Ensure the Dockerfile is placed in the services/frontend directory and properly configured for hot-reloading in development.

## 14. Update docker-compose.yml for new service paths and dev stages [done]
### Dependencies: None
### Description: Modify docker-compose.yml to reflect the new 'services' directory structure, use development targets for hot-reloading, and integrate Supabase local development.
### Details:


## 15. Create Makefile with 'dev-up' and 'dev-down' commands [done]
### Dependencies: None
### Description: Develop a Makefile in the root directory to simplify local development environment management with 'make dev-up' and 'make dev-down' commands.
### Details:


## 16. Verify local development environment with hot-reloading and Supabase [done]
### Dependencies: None
### Description: Confirm that 'make dev-up' starts all services with hot-reloading and Supabase local dev, and that all containers run without errors and basic tests pass.
### Details:


## 17. Create multi-stage Dockerfile for API Gateway (Golang) [done]
### Dependencies: 1.11
### Description: Implement a multi-stage Dockerfile for the Golang API Gateway service using 1.24-bookworm base image for all stages.
### Details:
Create a multi-stage Dockerfile for the api-gateway service following this example:

```dockerfile
FROM golang:1.24-bookworm as base

RUN adduser \
  --disabled-password \
  --gecos "" \
  --home "/nonexistent" \
  --shell "/sbin/nologin" \
  --no-create-home \
  --uid 65532 \
  small-user

WORKDIR $GOPATH/src/smallest-golang/app/

COPY . .

RUN go mod download
RUN go mod verify

RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /main .

FROM scratch

COPY --from=base /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=base /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=base /etc/passwd /etc/passwd
COPY --from=base /etc/group /etc/group

COPY --from=base /main .

USER small-user:small-user

CMD ["./main"]
```

Ensure the Dockerfile is placed in the services/api-gateway directory and properly configured for secure and small images. This follows best practices for creating minimal and secure Golang containers.

## 18. Manually initialize SvelteKit frontend with required tools [done]
### Dependencies: 1.3, 1.13
### Description: Manually set up the SvelteKit application in services/frontend with ESLint, Prettier, Tailwind CSS, and ShadcnUI.
### Details:
This is a manual task to be performed by the user:
1. Navigate to services/frontend directory
2. Initialize a new SvelteKit project
3. Configure ESLint and Prettier for code quality
4. Install and configure Tailwind CSS
5. Set up ShadcnUI component library
6. Verify the setup works correctly with the Docker development environment

## 19. Update Docker container naming conventions [done]
### Dependencies: 1.14
### Description: Rename containers to match the new naming convention: 'frontend', 'ai-service', and 'api-gateway'.
### Details:
Update all Docker-related files (Dockerfiles, docker-compose.yml) to use the new container names:
- 'frontend' for the SvelteKit service
- 'ai-service' for the Python service
- 'api-gateway' for the Golang service

Ensure all references and network configurations are updated accordingly.

## 20. Implement Python Dockerfile for ai-service container [done]
### Dependencies: 1.11, 1.12
### Description: Create the multi-stage Dockerfile for the Python ai-service container following the provided example.
### Details:
Create a Dockerfile in the services/ai-service directory using the following structure:

```dockerfile
# Stage 1: Builder
FROM python:3.12-bookworm AS builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .

# Stage 2: Development
FROM python:3.12-bookworm AS development
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
CMD ["python", "app.py"]

# Stage 3: Production
FROM distroless/python3-debian12
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /app .
CMD ["app.py"]
```

Ensure the Dockerfile is properly configured to create a secure and minimal production image while providing a full-featured development environment with hot-reloading capabilities.

## 21. Ensure version consistency for Python ai-service container [done]
### Dependencies: 1.12, 1.20
### Description: Verify that the Python ai-service container uses consistent Python 3.12 versions across all stages of the Dockerfile.
### Details:
Review the multi-stage Dockerfile for the ai-service to ensure:
1. The builder stage uses python:3.12-bookworm
2. The development stage uses python:3.12-bookworm
3. The production stage uses distroless/python3-debian12 that's compatible with Python 3.12
4. All Python package dependencies are consistent across environments
5. No version conflicts exist between the different stages

This task focuses specifically on ensuring version consistency to prevent subtle bugs that might occur from version mismatches between development and production environments.

